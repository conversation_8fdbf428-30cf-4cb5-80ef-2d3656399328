package com.gumtree.service.jobs;

import org.springframework.util.FileCopyUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Until the actual implementation is ready, stubbing CvService
 */
public class StubCvService implements CvService {

    private static Map<Long, StubCvData> cvStore = new ConcurrentHashMap<>();

    private static AtomicBoolean shouldFail = new AtomicBoolean(false);

    @Override
    public void upload(CvData requestCvData) throws IOException {
        failIfNecessary();
        Long userId = requestCvData.getUserId();

        CvData.Builder responseCvDataBuilder = new CvData.Builder(requestCvData);

        CvData responseCvData = responseCvDataBuilder.build();
        Path path = persistFile(responseCvData);
        StubCvData stubCvData = new StubCvData(path, responseCvData);

        cvStore.remove(userId);
        cvStore.put(userId, stubCvData);
    }

    @Override
    public Optional<CvData> getMetadata(Long userId) {
        failIfNecessary();
        if(cvStore.containsKey(userId)) {
            return Optional.of(cvStore.get(userId).getCvData());
        } else {
            return Optional.empty();
        }
    }

    @Override
    public Optional<CvData> getContent(Long userId) {
        failIfNecessary();
        if(cvStore.containsKey(userId)) {
            return Optional.of(cvStore.get(userId).getCvData());
        } else {
            return Optional.empty();
        }
    }

    @Override
    public void delete(Long userId) {
        failIfNecessary();
        cvStore.remove(userId);
    }

    public static Map<Long, StubCvData> getCvStore() {
        return cvStore;
    }

    public static void addFileToStore(CvData cvData) throws IOException {
        cvStore.put(cvData.getUserId(), new StubCvData(persistFile(cvData), cvData));
    }

    public static void clearAllData() {
        cvStore.clear();
    }

    public static void expectToFail() {
        shouldFail.set(true);
    }

    private static Path persistFile(CvData requestCvData) throws IOException {
        File file = File.createTempFile(requestCvData.getFilePrefix()+"-", "."+requestCvData.getFileSuffix());
        FileCopyUtils.copy(requestCvData.getBytes(), file);
        return Paths.get(file.getAbsolutePath());
    }

    private void failIfNecessary() {
        if (shouldFail.getAndSet(false)) {
            throw new RuntimeException("Expected failure");
        }
    }

}
