package com.gumtree.service.jobs.web;

import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import static com.google.common.io.Files.getFileExtension;

public final class CvFileValidator  {
    private static final Logger LOGGER = LoggerFactory.getLogger(CvFileValidator.class);
    private static final int MAX_FILE_SIZE = 8 * 1024 * 1024;

    private static final Set<String> VALID_EXTENSIONS = Sets.newHashSet(
            "pdf",
            "doc",
            "docx",
            "rtf"
    );

    private CvFileValidator() {
    }

    public static List<String> validate(MultipartFile multipartFile) {
        List<String> errors = new LinkedList<>();

        if (multipartFile.isEmpty()) {
            errors.add("upload.cv.empty.file");
        }

        String cvFilename = multipartFile.getOriginalFilename();
        if (!idHasValidExtension(cvFilename)) {
            errors.add("upload.cv.invalid.type");
        }

        long cvFileSize = multipartFile.getSize();
        if (cvFileSize > MAX_FILE_SIZE) {
            errors.add("upload.cv.file.too.large");
        }
        return errors;
    }

    private static boolean idHasValidExtension(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return VALID_EXTENSIONS.contains(extension);
    }
}
