package com.gumtree.util.cache;

/**
 * User: salamandr
 * Date: 22/04/2011
 * Time: 19:25
 *
 * @param <T> the type of the object that this service will cache
 */
public interface CacheService<T> {
    /**
     * Get the value associated with 'key', cached or freshly acquired
     *
     * @param namespace the namespace
     * @param key the key for the desired value
     * @return the value for the specified key
     */
    T get(CacheNamespace namespace, String key);
}
