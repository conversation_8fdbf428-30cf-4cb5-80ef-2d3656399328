package com.gumtree.util.http;

/**
 * Represents an url parameter
 * <AUTHOR>
 *
 */
public class UrlParameter implements Comparable<UrlParameter> {

    private final String name;
    private final String value;

    /**
     * Construct.
     * @param name of the parameter
     * @param value of the parameter
     */
    public UrlParameter(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public final String getName() {
        return name;
    }

    public final String getValue() {
        return value;
    }

    @Override
    public final int compareTo(UrlParameter o) {
        return name.compareToIgnoreCase(o.name);
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public final int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof UrlParameter)) {
            return false;
        }
        UrlParameter other = (UrlParameter) obj;
        if (name == null) {
            if (other.name != null) {
                return false;
            }
        } else if (!name.equals(other.name)) {
            return false;
        }
        if (value == null) {
            if (other.value != null) {
                return false;
            }
        } else if (!value.equals(other.value)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public final String toString() {
        return "UrlParameter [name=" + name + ", value=" + value + "]";
    }
}
