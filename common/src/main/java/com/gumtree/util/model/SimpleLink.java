package com.gumtree.util.model;


import org.apache.commons.lang.builder.HashCodeBuilder;

/**
 * Implementation of the {@link Link} interface.
 */
public final class SimpleLink implements Link {

    private String text;
    private String url;

    /**
     * Creates a new {@link SimpleLink}
     *
     * @param text text of the link
     * @param url  url of the link
     */
    public SimpleLink(String text, String url) {
        this.text = text;
        this.url = url;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public String getUrl() {
        return url;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof Link) {
            Link comparer = (SimpleLink) o;
            if (comparer.getText().equals(text)
                    && comparer.getUrl().equals(url)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(text).append(url).toHashCode();
    }

    /**
     * Get the full html tag as a String.  e.g. <a href="myurl.com">mytext</a>
     *
     * @return The HTML tag
     */
    public String getHTML() {
        return "<a href=\"" + getUrl() + "\">" + getText() + "</a>";
    }

    @Override
    public String toString() {
        return "SimpleLink{" +
                "text='" + text + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
