package com.gumtree.location;

import com.gumtree.common.util.StringUtils;
import com.gumtree.api.category.domain.CategoryConstants;
import org.apache.commons.lang.WordUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * Location display utilities
 */
public final class LocationDisplayUtils {

    private static final Set<Long> INTERNATIONAL = new HashSet<Long>();

    static {
        INTERNATIONAL.add(CategoryConstants.INTERNATIONAL_PROPERTY_ID);
        INTERNATIONAL.add(CategoryConstants.EUROPEAN_PROPERTY_ID);
        INTERNATIONAL.add(CategoryConstants.REST_OF_THE_WORD_PROPERTY_ID);
    }


    private LocationDisplayUtils() {

    }

    /**
     * Upper first letter, lower case for remainder, display
     * name of landing location if no location text given.
     * @param locationText maybe null
     * @param landingLocationDisplayName maybe null
     * @param categoryId maybe null
     * @return empty string if both text and landingLocation are null
     */
    public static String getCleanedLocationText(String locationText,
                                                String landingLocationDisplayName,
                                                Long categoryId) {
        String cleanedLocationText = getCleanedLocationText(locationText);

        if (INTERNATIONAL.contains(categoryId)) { //gtpub-939 INTERNATIONAL properties should not have landing appended
            return cleanedLocationText;
        }

        if (landingLocationDisplayName != null
                && (cleanedLocationText == null
                || !equalsWhenStripped(cleanedLocationText, landingLocationDisplayName))) {
            if (StringUtils.hasText(cleanedLocationText)) {
                cleanedLocationText = StringUtils.concat(cleanedLocationText, ", ", landingLocationDisplayName);
            } else {
                cleanedLocationText = landingLocationDisplayName;
            }
        }

        return cleanedLocationText != null ? cleanedLocationText : "";
    }

    /**
     * Upper first letter, lower case for remainder.
     *
     * @param locationText maybe null
     * @return empty string if location text is null
     */
    public static String getCleanedLocationText(String locationText) {
        if (!StringUtils.hasText(locationText)) {
            return "";
        }

        if (locationText.matches(StringUtils.ZIP_OUTCODE_REGEX) || locationText.matches(StringUtils.ZIP_CODE_REGEX)) {
            return locationText.toUpperCase();
        }

        return WordUtils.capitalizeFully(locationText.trim());
    }


    private static boolean equalsWhenStripped(String first, String second) {
        return stripForComparison(first).equalsIgnoreCase(stripForComparison(second));
    }

    private static String stripForComparison(String dirtyString) {
        if (dirtyString == null) {
            return "";
        }

        return dirtyString.replaceAll("\\W", "");
    }


}
