package com.gumtree.common.model.location.impl;

import com.gumtree.common.model.location.LocationModel;
import com.gumtree.common.model.location.LocationModelManager;
import com.gumtree.util.stub.api.StubLocationApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Stub Location Model Manager
 */
public final class StubLocationModelManager implements LocationModelManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(StubLocationModelManager.class);

    private ApiLocationModel apiLocationModel;

    /**
     * Constructor.
     *
     * @param locationApi the stub location api
     */
    public StubLocationModelManager(StubLocationApi locationApi) {
        apiLocationModel = new ApiLocationModel(locationApi);
        LOGGER.warn("************** USING STUBBED LOCATION MODEL **************");
    }

    @Override
    public LocationModel getLocationModel() {
        return apiLocationModel;
    }
}
