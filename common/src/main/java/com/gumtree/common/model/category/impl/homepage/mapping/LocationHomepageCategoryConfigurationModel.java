package com.gumtree.common.model.category.impl.homepage.mapping;

import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;

/**
 * Location Homepage Category Configuration Model (for mapping from JSON config file).
 */
public final class LocationHomepageCategoryConfigurationModel {
    private static final Logger LOGGER = LoggerFactory.getLogger(LocationHomepageCategoryConfigurationModel.class);

    @Autowired
    private ObjectMapper objectMapper;

    private List<LocationHomepageCategory> categories;

    public List<LocationHomepageCategory> getCategories() {
        return categories;
    }

    public void setCategories(List<LocationHomepageCategory> categories) {
        this.categories = categories;
    }

    @PostConstruct
    public void init() {
        try {
            LocationHomepageCategoryConfigurationModel locationHomepageCategoryConfigurationModel =
                    objectMapper.readValue(new ClassPathResource("homepage-category-configuration-proserv.json")
                            .getInputStream(),
                    LocationHomepageCategoryConfigurationModel.class);
           categories = locationHomepageCategoryConfigurationModel.getCategories();
        } catch (IOException e) {
            LOGGER.error("Problem occurred while loading location homepage category model", e);
        }

    }
}
