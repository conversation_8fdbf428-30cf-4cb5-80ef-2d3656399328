package com.gumtree.search;

import com.gumtree.domain.location.Location;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Represents a location the user searches in.
 *
 * <AUTHOR>
 */
public class UserSearchLocation {

    public static final UserSearchLocation EMPTY
            = new UserSearchLocation(null, new ArrayList<String>(), new ArrayList<String>(), "");

    private final Location location;
    private final List<String> locationSearchTerms;
    private final List<String> zipCodes;
    private final String userInput;

    /**
     * Construct.
     *
     * @param location not null
     */
    public UserSearchLocation(Location location) {
        this(location, Collections.<String>emptyList(), Collections.<String>emptyList(), "");
    }

    /**
     * Construct.
     *
     * @param location            of the search
     * @param locationSearchTerms additional terms
     * @param zipCodes            recognized
     * @param userInput           the actual input
     */
    public UserSearchLocation(Location location,
                              List<String> locationSearchTerms,
                              List<String> zipCodes, String userInput) {
        this.location = location;
        this.locationSearchTerms = new ArrayList<String>(locationSearchTerms);
        this.userInput = userInput;
        this.zipCodes = new ArrayList<String>(zipCodes);
    }

    public Location getLocation() {
        return location;
    }

    public final List<String> getLocationSearchTerms() {
        return locationSearchTerms;
    }

    public final String getUserInput() {
        return userInput;
    }

    public final List<String> getZipOutCodes() {
        return zipCodes;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public final int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result
                + ((location == null) ? 0 : location.hashCode());
        result = prime
                * result
                + ((locationSearchTerms == null) ? 0 : locationSearchTerms
                .hashCode());
        result = prime * result
                + ((userInput == null) ? 0 : userInput.hashCode());
        result = prime * result
                + ((zipCodes == null) ? 0 : zipCodes.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof UserSearchLocation)) {
            return false;
        }
        UserSearchLocation other = (UserSearchLocation) obj;
        if (location == null) {
            if (other.location != null) {
                return false;
            }
        } else if (!location.equals(other.location)) {
            return false;
        }
        if (locationSearchTerms == null) {
            if (other.locationSearchTerms != null) {
                return false;
            }
        } else if (!locationSearchTerms.equals(other.locationSearchTerms)) {
            return false;
        }
        if (userInput == null) {
            if (other.userInput != null) {
                return false;
            }
        } else if (!userInput.equals(other.userInput)) {
            return false;
        }
        if (zipCodes == null) {
            if (other.zipCodes != null) {
                return false;
            }
        } else if (!zipCodes.equals(other.zipCodes)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public final String toString() {
        return "UserSearchLocation [location=" + location
                + ", locationSearchTerms=" + locationSearchTerms
                + ", zipOutCodes=" + zipCodes + ", userInput=" + userInput
                + "]";
    }
}
