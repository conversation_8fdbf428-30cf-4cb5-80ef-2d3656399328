package com.gumtree.domain.newattribute.internal.value;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;

/**
 * An date value.
 *
 * If you change something don't forget to overwrite equals and hashcode.
 * <AUTHOR>
 *
 */
public final class DateValue implements AttributeValue {
    private static final String OUT_PATTERN = "dd/MM/yyyy";
    private static final String IN_PATTERN = "yyyyMMdd";

    private final Date date;


    /**
     * @param date a date or a string
     */
    private DateValue(Date date) {
       this.date = date;
    }

    @Override
    public String getName() {
        return as(String.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T as(Class<T> clazz) {
        if (String.class.isAssignableFrom(clazz)) {
            SimpleDateFormat df = new SimpleDateFormat(OUT_PATTERN);
            return (T) df.format(date);
        }

        if (Date.class.isAssignableFrom(clazz)) {
            return (T) date;
        }

        return null;
    }

    @Override
    public String toString() {
      DateFormat dateToString = new SimpleDateFormat("dd/MM/yy");
      return dateToString.format(date);
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((date == null) ? 0 : date.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof DateValue)) {
            return false;
        }
        DateValue other = (DateValue) obj;
        if (date == null) {
            if (other.date != null) {
                return false;
            }
        } else if (!date.equals(other.date)) {
            return false;
        }
        return true;
    }

    /**
     * Creates DateValue attribute value
     *
     * @param o - The object o
     * @return - DateValue instance
     * @throws InvalidAttributeValueException
     *             - If error creating InvalidAttributeValue
     */
    public static AttributeValue create(Object o) throws InvalidAttributeValueException {
        Date date;
        if (o instanceof Date) {
            date =  (Date) o;
        } else if (o instanceof String) {
            try {
                date = new SimpleDateFormat(IN_PATTERN).parse((String) o);
            } catch (ParseException e) {
                throw new InvalidAttributeValueException(e);
            }
        } else {
            throw new InvalidAttributeValueException("Could not interpret object "
                                                                + o + " of type " + o.getClass() + ".");
        }

        return new DateValue(date);
    }
}
