package com.gumtree.domain.newattribute.internal;

import org.codehaus.jackson.map.annotate.JsonDeserialize;

import java.util.List;

/**
 */
public class AttributeMetadata {

    @JsonDeserialize
    private List<AttributeGroup> categories;

    /**
     * Get attribute => category mappings
     *
     * @return attribute definitions grouped by category
     */
    public final List<AttributeGroup> getGroups() {
        return categories;
    }
}
