package com.gumtree.domain.newattribute.internal;

import com.gumtree.api.category.domain.SearchAttribute;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.newattribute.DisplayAttributeType;
import com.gumtree.domain.newattribute.value.Range;

import java.util.List;

/**
 * Default implementation.
 * <AUTHOR>
 *
 */
public class DefaultDisplayAttributeType implements DisplayAttributeType {
    private final String name;
    private final String displayName;
    private final List<DisplayAttribute> attributes;
    private final String selectedValue;
    private final SearchAttribute.Style searchStyle;
    private final Range selectedRange;

    /**
     * DefaultDisplayAttributeType
     * @param name name
     * @param displayName displayName
     * @param attributes attributes
     * @param searchStyle searchStyle
     * @param selectedValue selectedValue
     * @param selectedRange selectedRange
     */
    public DefaultDisplayAttributeType(String name,
                                       String displayName,
                                       List<DisplayAttribute> attributes,
                                       SearchAttribute.Style searchStyle,
                                       String selectedValue,
                                       Range selectedRange) {
        this.name = name;
        this.displayName = displayName;
        this.attributes = attributes;
        this.searchStyle = searchStyle;
        this.selectedValue = selectedValue;
        this.selectedRange = selectedRange;
    }

    @Override
    public final String getName() {
        return name;
    }

    @Override
    public final String getDisplayName() {
        return displayName;
    }

    @Override
    public final List<DisplayAttribute> getAttributes() {
        return attributes;
    }

    @Override
    public SearchAttribute.Style getSearchStyle() {
        return searchStyle;
    }

    @Override
    public final String getSelectedValue() {
        return selectedValue;
    }

    @Override
    public final DisplayAttribute getFirstAttribute() {
        if (attributes.isEmpty()) {
            return null;
        }
        return attributes.get(0);
    }

    @Override
    public final Range getSelectedRange() {
        return selectedRange;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public final int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result
                + ((attributes == null) ? 0 : attributes.hashCode());
        result = prime * result
                + ((displayName == null) ? 0 : displayName.hashCode());
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result
                + ((searchStyle == null) ? 0 : searchStyle.hashCode());
        result = prime * result
                + ((selectedRange == null) ? 0 : selectedRange.hashCode());
        result = prime * result
                + ((selectedValue == null) ? 0 : selectedValue.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof DefaultDisplayAttributeType)) {
            return false;
        }
        DefaultDisplayAttributeType other = (DefaultDisplayAttributeType) obj;
        if (attributes == null) {
            if (other.attributes != null) {
                return false;
            }
        } else if (!attributes.equals(other.attributes)) {
            return false;
        }
        if (displayName == null) {
            if (other.displayName != null) {
                return false;
            }
        } else if (!displayName.equals(other.displayName)) {
            return false;
        }
        if (name == null) {
            if (other.name != null) {
                return false;
            }
        } else if (!name.equals(other.name)) {
            return false;
        }
        if (searchStyle != other.searchStyle) {
            return false;
        }
        if (selectedRange == null) {
            if (other.selectedRange != null) {
                return false;
            }
        } else if (!selectedRange.equals(other.selectedRange)) {
            return false;
        }
        if (selectedValue == null) {
            if (other.selectedValue != null) {
                return false;
            }
        } else if (!selectedValue.equals(other.selectedValue)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public final String toString() {
        return "DefaultDisplayAttributeType [name=" + name + ", displayName="
                + displayName + ", attributes=" + attributes
                + ", selectedValue=" + selectedValue + ", searchStyle="
                + searchStyle + ", selectedRange=" + selectedRange + "]";
    }
}
