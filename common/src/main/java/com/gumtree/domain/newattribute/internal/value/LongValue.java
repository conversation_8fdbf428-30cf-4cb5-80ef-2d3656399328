package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.api.domain.savedsearches.SearchRangeBean;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.value.Range;
import org.springframework.util.Assert;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;

/**
 * An int value or range
 * If you change something don't forget to overwrite equals and hashcode.
 * <AUTHOR>
 *
 */
public final class LongValue implements AttributeValue {

    private static Range createRange(String str) throws InvalidAttributeValueException {
        Long min = null;
        Long max = null;
        if (str.startsWith("over_")) {
            min = Long.parseLong(str.substring("over_".length())) + 1;
        } else {
            String[] parts = str.split("_to_");
            if (parts.length != 2) {
                throw new InvalidAttributeValueException("Value '" + str + "' is not a valid integer range.");
            }
            if (parts[0].equals("up")) {
                min = 0L;
            } else {
                min = Long.parseLong(parts[0]);
            }
            max = Long.parseLong(parts[1]);
        }

        return new Range(min, max);

    }

    private final Long value;
    private final Range range;
    private final String unit;

    /**
     * @param v integer, string, list
     * @param unit maybe null
     */
   LongValue(Long v, Range r, String unit) {
        this.unit = unit;
        this.value = v;
        this.range = r;
    }

    @Override
    public String getName() {
        return as(String.class);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T as(Class<T> clazz) {
        if (Range.class.isAssignableFrom(clazz)) {
            return (T) range;
        }

        if (Integer.class.isAssignableFrom(clazz)) {
            return (T) value;
        }

        if (String.class.isAssignableFrom(clazz)) {
            if (value != null) {
                return (T) value.toString();
            }

            if (range != null) {
                return (T) getNameForRange();
            }
        }
        throw new IllegalArgumentException("Could not convert integer value to " + clazz + ".");
    }

    private String getNameForRange() {
        Long min = range.getMin();
        Long max = range.getMax();
        Assert.isTrue(min != null || max != null);

        if (min != null && max != null) {
            if (min > 0) {
                return min + "_to_" + max;
            }
            return "up_to_" + max;
        }

        if (min != null) {
            return "over_" + (min - 1);
        }

        return "up_to_" + max;
    }

   @Override
    public String toString() {
       NumberFormat numberFormat =  NumberFormat.getIntegerInstance(Locale.ENGLISH);
       StringBuilder sb = new StringBuilder();
       if (value != null) {
           sb.append(numberFormat.format(value));
       } else {
           if (range.getMin() != null) {
               if (range.getMin().equals(0)) {
                   sb.append("Up to ");
               } else {
                   if (range.getMax() == null) {
                       sb.append("Over ");
                       sb.append(numberFormat.format(range.getMin() - 1));
                   } else {
                       sb.append(numberFormat.format(range.getMin()));
                       sb.append(" - ");
                   }
               }
           }

           if (range.getMax() != null) {
               sb.append(numberFormat.format(range.getMax()));
           }
       }

       if (unit != null) {
           sb.append(" ").append(unit);
       }

       return sb.toString();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((range == null) ? 0 : range.hashCode());
        result = prime * result + ((unit == null) ? 0 : unit.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof LongValue)) {
            return false;
        }
        LongValue other = (LongValue) obj;
        if (range == null) {
            if (other.range != null) {
                return false;
            }
        } else if (!range.equals(other.range)) {
            return false;
        }
        if (unit == null) {
            if (other.unit != null) {
                return false;
            }
        } else if (!unit.equals(other.unit)) {
            return false;
        }
        if (value == null) {
            if (other.value != null) {
                return false;
            }
        } else if (!value.equals(other.value)) {
            return false;
        }
        return true;
    }

    /**
     * Creates IntegerValue attribute value
     *
     * @param o
     *            - The object o
     * @param unit
     *            - Any unit representation for this integer value
     * @return - IntegerValue instance
     * @throws InvalidAttributeValueException
     *             - If error creating InvalidAttributeValue
     */
    public static AttributeValue create(Object o, String unit) throws InvalidAttributeValueException {
        if (o instanceof List) {
            @SuppressWarnings({ "unchecked", "rawtypes" })
            List<Object> list = (List) o;
            if (!list.isEmpty()) {
                o = list.get(0);
            }
        }

        Long v = null;
        Range r = null;
        if (o instanceof Long) {
            v = (Long) o;
            r = new Range(v, v);
        } else if(o instanceof Integer) {
            v = ((Integer) o).longValue();
            r = new Range(v, v);
        } else if (o instanceof String) {
            String str = (String) o;
            try {
                v = (long) Float.parseFloat(str.replace(",", ""));
            } catch (NumberFormatException e) {
                r = createRange(str);
            }
        } else if (o instanceof Range) {
            r = (Range) o;
        } else if (o instanceof SearchRangeBean) {
            r = new Range(((SearchRangeBean) o).getMinValue(), ((SearchRangeBean) o).getMaxValue());
        } else if(o instanceof Double) {
            v = ((Double) o).longValue();
            r = new Range(v, v);
        }
        if (v == null && r == null) {
            throw new InvalidAttributeValueException("Could not interpret object " + o + " Type: " + o.getClass());

        }

        return new LongValue(v, r, unit);
    }
}
