package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;

import java.util.Calendar;
import java.util.Date;

/**
 *
 * If you change something don't forget to overwrite equals and hashcode.
 * <AUTHOR>
 *
 */
public final class YearValue implements AttributeValue {
    private final Integer year;

    /**
     * @param year date, string
     */
    private YearValue(Integer year) {
        this.year = year;
    }

    @Override
    public String getName() {
        return as(String.class);
    }

    @Override
    public <T> T as(Class<T> clazz) {
        if (String.class.isAssignableFrom(clazz)) {
            return (T) year.toString();
        }

        return null;
    }

    @Override
    public String toString() {
        return year.toString();
    }


    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((year == null) ? 0 : year.hashCode());
        return result;
    }


    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof YearValue)) {
            return false;
        }
        YearValue other = (YearValue) obj;
        if (year == null) {
            if (other.year != null) {
                return false;
            }
        } else if (!year.equals(other.year)) {
            return false;
        }
        return true;
    }

    /**
     * Creates YearValue attribute value
     *
     * @param o
     *            - The object o
     * @return - YearValue instance
     * @throws InvalidAttributeValueException
     *             - If error creating InvalidAttributeValue
     */
    public static AttributeValue create(Object o) throws InvalidAttributeValueException {
        Integer year;
        if (o instanceof Date) {
            Date date = (Date) o;
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            year = cal.get(Calendar.YEAR);
        } else if (o instanceof String) {
            year = Integer.parseInt((String) o);
        } else if (o instanceof Long) {
            year = ((Long) o).intValue();
        } else if (o instanceof Integer) {
            year = (Integer) o;
        } else {
            throw new InvalidAttributeValueException("Could not interpret object " + o);
        }
        return new YearValue(year);
    }
}
