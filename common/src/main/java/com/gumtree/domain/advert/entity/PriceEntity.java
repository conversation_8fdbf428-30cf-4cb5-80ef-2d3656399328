package com.gumtree.domain.advert.entity;

import com.gumtree.domain.advert.Price;
import com.gumtree.util.json.jackson.deserializer.StringToCurrencyDeserializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;

import java.math.BigDecimal;
import java.util.Currency;

/**
 * Default implementation of {@link Price}
 */
public class PriceEntity implements Price {

    @JsonDeserialize(using = StringToCurrencyDeserializer.class)
    private Currency currency;

    @JsonDeserialize
    private BigDecimal amount;

    @Override
    public final Currency getCurrency() {
        return currency;
    }

    public final void setCurrency(Currency currency) {
        this.currency = currency;
    }

    @Override
    public final BigDecimal getAmount() {
        return amount;
    }

    public final void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public final String toString() {
        return "PriceEntity{"
                + "amount=" + amount
                + '}';
    }
}
