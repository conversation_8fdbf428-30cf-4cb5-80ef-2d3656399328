package com.gumtree.domain.advert;

import com.gumtree.domain.feed.Feed;
import com.gumtree.domain.location.LocationCentroid;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.user.User;

import java.util.Collection;

/**
 * A model to represent a classified advert
 */
public interface Advert extends BasicAdvert {

    /**
     * @return the advert price information
     */
    Price getPrice();

    /**
     * @return get all dynamic {@link Attribute}s associated with this advert
     */
    Collection<Attribute> getAttributes();

    /**
     * @param attributeType id of the {@link Attribute} to retrieve
     * @return the dynamic {@link Attribute} with the given id
     */
    Attribute getAttribute(String attributeType);


    /**
     * @return the geographic point that this advert is bound to
     */
    LocationCentroid getPoint();

    /**
     * @return the free text user defined location bound to this advert
     */
    String getLocationText();

    /**
     * @return the postcode this advert is bound to
     */
    String getPostcode();

    /**
     * @return the main {@link Image} associated with this advert
     */
    Image getMainImage();

    /**
     * @return the additional {@link Image}s associated with this advert
     */
    Collection<Image> getImages();

    /**
     * @return the {@link Video}s associated with this advert
     */
    Collection<Video> getVideos();

    /**
     * @return details of the user who posted this advert
     */
    User getPostingUser();

    /**
     * @return if the advert is visible on a map or not
     */
    boolean isVisibleOnMap();

    /**
     * @return if the advert is urgent or not
     */
    boolean isUrgent();

    /**
     * @return if the advert is spotlighted or not
     */
    boolean isSpotlighted();

    /**
     * @return the originating feed or null if not posted via a feed
     */
    Feed getFeed();

    /**
     * @return if the advert is featured or not
     */
    boolean isFeatured();

    /**
     * @return the website link of the advert
     */
    String getWebsiteLink();

    Boolean isPaidFor();

    /**
     * @return true if this ad was posted using a pro account
     */
    Boolean isProAccountAd();
}