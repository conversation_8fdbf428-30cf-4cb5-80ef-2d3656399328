package com.gumtree.thirdparty;

/**
 * A request builder factory builds request builder and also populates
 * the properties for a given tile. Required properties are passed as arguments
 * to the builder as it is created.
 *
 * @param <T> the {@link RequestBuilder} type
 */
public interface RequestBuilderFactory<T extends RequestBuilder> {

    /**
     * Create {@link RequestBuilder}
     *
     * @return a {@link RequestBuilder} instance
     */
    T create();
}
