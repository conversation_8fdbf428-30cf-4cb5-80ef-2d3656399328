package com.gumtree.util.cache.memcached;

import com.gumtree.util.cache.CacheValueFactory;

import java.util.UUID;

/**
 * User: rajisingh
 * Date: 20/04/11
 * Time: 07:22
 */
public class StringCacheValueFactory implements CacheValueFactory<String> {
    /**
     * Test value factory for playing with CacheService
     *
     * @param key the key to fetch a value for
     * @return the newly built value
     */
    public String create(String key) {
        return key + UUID.randomUUID().toString();
    }
}

