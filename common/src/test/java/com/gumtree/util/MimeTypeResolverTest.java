package com.gumtree.util;

import com.gumtree.util.mime.AcceptedMime;
import com.gumtree.util.mime.WrongMimeTypeException;
import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static com.gumtree.util.MimeTypeResolver.resolveCVContentType;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class MimeTypeResolverTest {

    @Test
    public void shouldReturnTheRightContentTypeForAcceptedFormats() throws IOException {
        //given
        String[][] realAcceptedFiles = {
                {"src/test/resources/com/gumtree/mime/real.doc", MimeType.DOC.getContentType(), AcceptedMime.DOC.getContentType()},
                {"src/test/resources/com/gumtree/mime/real.docx", MimeType.DOC_X.getContentType(), AcceptedMime.DOC_X.getContentType()},
                {"src/test/resources/com/gumtree/mime/real.pdf", MimeType.PDF.getContentType(), AcceptedMime.PDF.getContentType()},
                {"src/test/resources/com/gumtree/mime/real.rtf", MimeType.RTF.getContentType(), AcceptedMime.RTF.getContentType()}
        };

        for (String[] fileAndContent : realAcceptedFiles){
            // given
            MultipartFile multipartFile = fileToMultipartFile(fileAndContent[0], fileAndContent[1]);

            // when
            String result = resolveCVContentType(multipartFile);

            // then
            assertThat(result, equalTo(fileAndContent[1]));
        }
    }

    @Test(expected = WrongMimeTypeException.class)
    public void shouldThrowExceptionForRejectedFormats() throws IOException {
        // given
        List<String> realRejectedFiles = Arrays.asList(
                "src/test/resources/com/gumtree/mime/real.txt",
                "src/test/resources/com/gumtree/mime/real.jpeg"
        );

        for (String file : realRejectedFiles){
            resolveCVContentType(fileToMultipartFile(file, MimeType.TXT.getContentType()));
        }
    }

    @Test(expected = WrongMimeTypeException.class)
    public void shouldThrowExceptionForFakeFilesWithRejectedFormat() throws IOException {
        // given
        List<String> fakeFiles = Arrays.asList(
                    "src/test/resources/com/gumtree/mime/fake.doc",
                    "src/test/resources/com/gumtree/mime/fake.docx",
                    "src/test/resources/com/gumtree/mime/fake.pdf",
                    "src/test/resources/com/gumtree/mime/fake.rtf"
        );

        for (String file : fakeFiles){
            resolveCVContentType(fileToMultipartFile(file, MimeType.TXT.getContentType()));
        }
    }

    private MultipartFile fileToMultipartFile(String filePath, String contentType){
        Path path = Paths.get(filePath);
        String[] splits = filePath.split("/");
        String name = splits[splits.length - 1];
        String originalFileName = splits[splits.length - 1];
        byte[] content = null;
        try {
            content = Files.readAllBytes(path);
        } catch (final IOException e) {}
        return  new MockMultipartFile(name, originalFileName, contentType, content);
    }

    private enum MimeType {
        TXT("text/plain"),
        JPEG("image/jpeg"),
        DOC("application/msword"),
        RTF("application/rtf"),
        PDF("application/pdf"),
        DOC_X("application/vnd.openxmlformats-officedocument.wordprocessingml.document");

        private final String contentType;

        MimeType(String contentType) {
            this.contentType = contentType;
        }

        public String getContentType() {
            return contentType;
        }
    }
}