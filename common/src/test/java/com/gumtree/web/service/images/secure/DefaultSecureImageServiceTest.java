package com.gumtree.web.service.images.secure;

import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class DefaultSecureImageServiceTest {

    private DefaultSecureImageService secureImageService;

    @Before
    public void setUp(){
        secureImageService = new DefaultSecureImageService("https://ssli.ebayimg.com");
    }

    @Test
    public void getSecureUrl(){
        // given
        String url = "http://i.ebayimg.com/image/path.jpg";

        // when
        String secureUrl = secureImageService.getSecureUrl(url);

        // then
        assertThat(secureUrl, equalTo("https://ssli.ebayimg.com/image/path.jpg"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void throwsExceptionOnMalformedUrl(){
        // given
        String url = "!@#$%^&*()";

        // when
        secureImageService.getSecureUrl(url);

        // then
        // expect exception
    }

}
