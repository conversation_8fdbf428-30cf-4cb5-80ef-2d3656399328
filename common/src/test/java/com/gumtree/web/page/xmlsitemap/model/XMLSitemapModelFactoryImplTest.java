package com.gumtree.web.page.xmlsitemap.model;

import org.junit.Test;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * Unit tests for XMLSitemapModelFactoryImpl.
 */
public class XMLSitemapModelFactoryImplTest {

    /**
     * Test basic sitemap URL creation.
     * @throws URISyntaxException only if the test fails
     */
    @Test
    public void testCreateXMLSitemapURL() throws URISyntaxException {
        URI uri = new URI("https://hello/");
        Calendar calendar = Calendar.getInstance();
        ChangeFreq freq = ChangeFreq.DAILY;
        int priority = 1;

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        XMLSitemapURL url = factory.createXMLSitemapURL(uri,freq, priority, calendar);
        
        assertEquals(uri,url.getLoc());
        assertEquals(freq,url.getChangeFreq());
        assertEquals(priority,url.getPriority());
        assertEquals(calendar,url.getLastMod());
        assertEquals(freq.toString().toLowerCase(),url.getLowerCaseChangeFreq());
        assertEquals("0.1",url.getFormattedPriority());
    }

    /**
     * Test that priority out of range throws an exception.
     * @throws URISyntaxException if test passes
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreateXMLSitemapURLPriorityTooLarge() throws URISyntaxException {
        URI uri = new URI("http://hello/");
        Calendar calendar = Calendar.getInstance();
        ChangeFreq freq = ChangeFreq.DAILY;
        int priority = 11;

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        factory.createXMLSitemapURL(uri,freq, priority, calendar);
    }

    /**
     * Test that priority out of range throws an exception.
     * @throws URISyntaxException if test passes
     */
    @Test(expected = IllegalArgumentException.class)
    public void testCreateXMLSitemapURLPriorityTooSmall() throws URISyntaxException {
        URI uri = new URI("http://hello/");
        Calendar calendar = Calendar.getInstance();
        ChangeFreq freq = ChangeFreq.DAILY;
        int priority = -1;

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        factory.createXMLSitemapURL(uri,freq, priority, calendar);
    }

    /**
     * Test creation of an XMLSitemap object.
     */
    @Test
    public void testCreateXMLSitemap() {
        List<XMLSitemapURL> urls = new ArrayList<XMLSitemapURL>();

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        XMLSitemap xmlSitemap = factory.createXMLSitemap(urls);

        assertEquals(urls,xmlSitemap.getXMLSitemapURLs());
    }

    /**
     * Test creation of a sitemap index URL.
     * @throws URISyntaxException only if test fails
     */
    @Test
    public void testCreateXMLSitemapIndexURL() throws URISyntaxException {
        URI uri = new URI("https://hello/");
        Calendar calendar = Calendar.getInstance();

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        XMLSitemapIndexURL url = factory.createXMLSitemapIndexURL(uri, calendar);

        assertEquals(uri,url.getLoc());
        assertEquals(calendar,url.getLastMod());
    }

    /**
     * Test creation of a sitemap index object.
     */
    @Test
    public void testCreateXMLSitemapIndex() {
        List<XMLSitemapIndexURL> urls = new ArrayList<XMLSitemapIndexURL>();

        XMLSitemapModelFactoryImpl factory = new XMLSitemapModelFactoryImpl();
        XMLSitemapIndex xmlSitemapIndex = factory.createXMLSitemapIndex(urls);

        assertEquals(urls,xmlSitemapIndex.getXMLSitemapIndexURLs());
    }
}
