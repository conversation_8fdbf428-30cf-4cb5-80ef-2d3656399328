package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationBegin;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserActivationBeginEventConverter extends AbstractZenoEventConverter<UserActivationBeginZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserActivationBeginEventConverter(ZenoConverterService zenoConverterService,
                                             RequestDetailsService requestDetailsService) {
        super(UserActivationBeginZenoEvent.class, zenoConverterService);

        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserActivationBeginZenoEvent userActivationFailureZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserActivationResend);
        // as the user isn't logged in, requestDetailsService.getUserData() is empty
        UserData userData = UserData.aUser()
                .withLoggedIn(false)
                .withLoginFailure(false)
                .withUserEmail(userActivationFailureZenoEvent.getUser().getEmail())
                .withUserId(userActivationFailureZenoEvent.getUser().getId())
                .withAccountType(UserData.AccountType.Standard)
                .build();

        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserActivationBegin(pageData, userData, deviceData);
    }
}
