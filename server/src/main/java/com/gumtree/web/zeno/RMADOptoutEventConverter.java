package com.gumtree.web.zeno;

import com.gumtree.zeno.core.domain.ManageAdsData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.RMADOptout;
import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * Converter for Zeno logging of people opting out of RMAD page
 * User: pvillega
 */
@Component
public class RMADOptoutEventConverter extends AbstractEventConverter<Object[], String, RMADOptout> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public RMADOptoutEventConverter(ZenoConverterService zenoConverterService,
                                    RequestDetailsService requestDetailsService) {
        super(RMADOptout.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public RMADOptout convertToEvent(Object[] input, String output) {
        Assert.isInstanceOf(String.class, input[0]);
        Assert.isInstanceOf(String.class, input[1]);
        String override = (String) input[0];
        String responsiveGroup = (String) input[1];

        PageData pageData = requestDetailsService.getPageData(PageType.MyAds);
        UserData userData = requestDetailsService.getUserData();
        ManageAdsData manageAdsData = ManageAdsData.aManageAds()
                .withOverride(override)
                .withResponsiveGroup(responsiveGroup)
                .build();

        return new RMADOptout(pageData, userData, requestDetailsService.getDeviceData(), manageAdsData);
    }

}
