package com.gumtree.web.zeno;

import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.utils.TrackingUtils;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.shiro.GumtreeFormAuthenticationFilter;
import com.gumtree.zeno.core.domain.TestGroupsData;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.gumtree.web.zeno.DeviceTypeHelper.getDeviceTypeZenoValue;
import static com.gumtree.zeno.core.domain.TestGroupsData.testGroups;

@Service
public class SellerZenoHelperService extends CommonZenoHelperService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SellerZenoHelperService.class);

    private static final String PLATFORM_NAME = "bushfire-desktop-seller";

    @Autowired
    private UserSession userSession;

    @Autowired
    private ExperimentsProvider experimentsProvider;

    @Autowired
    private UserSessionService userSessionService;

    @Override
    protected void doPreHandle(RequestDetailsService.Builder requestDetailsInitialiser) {
        HttpServletRequest currentRequest = getCurrentRequest();

        requestDetailsInitialiser
                .withPlatformName(PLATFORM_NAME)
                .withDeviceType(getDeviceTypeZenoValue(currentRequest))
                .withUserCookieId(userSessionService.getPermanentCookieId())
                .withUserCookieId(TrackingUtils.generateHashedClientId(userSessionService.getPermanentCookieId()))
                .withSessionCookieId(userSessionService.getSessionCookieId())
                .withNewCookie(userSessionService.isNew())
                .withTestGroupsData(fromABTestingContext());

        if (userSession.isAuthenticated()) {
            User user = userSession.getUser();
            if (user.getId() != null && !currentRequest.getRequestURI().equals("/logout")) {
                requestDetailsInitialiser.withLoggedInUserEmail(user.getEmail())
                        .withLoggedInUserType(user.getType() == UserType.PRO
                                ? UserData.AccountType.Pro
                                : UserData.AccountType.Standard)
                        .withUserId(user.getId());
            }
        }
        if (currentRequest.getAttribute(GumtreeFormAuthenticationFilter.LOGIN_FAILURE) != null) {
            requestDetailsInitialiser.withLoginFailure(true);
        } else {
            requestDetailsInitialiser.withLoginFailure(false);
        }

        requestDetailsInitialiser.initialise();
    }

    private TestGroupsData fromABTestingContext() {
        TestGroupsData.Builder builder = testGroups();
        LOGGER.debug("setting Zeno tg field");

        Experiments experiments = experimentsProvider.get();
        Map<String, String> myExperiments = experiments.getExperiments();
        for(Map.Entry<String, String> part: myExperiments.entrySet()) {
            builder.withPageLevelGroup(part.getKey(), part.getValue());
        }
        return builder.build();
    }
}
