package com.gumtree.web.zeno;

import com.gumtree.domain.advert.Advert;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdCategory;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.fulladsearch.model.FullAdLocation;
import com.gumtree.web.common.page.context.RequestScopedGumtreePageContext;
import com.gumtree.zeno.core.converter.impl.GenericPageEventConverter;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.HierarchicalData;
import com.gumtree.zeno.core.integration.AbstractZenoHelperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.ModelAndView;

public abstract class CommonZenoHelperService extends AbstractZenoHelperService<FullAdFlatAd> {
    @Autowired
    private RequestScopedGumtreePageContext gumtreePageContext;
    @Autowired
    private HierarchyUtil hierarchyUtil;
    @Autowired
    private ToAdvertDataConverter toAdvertDataConverter;
    @Autowired
    private FullAdsSearchApi fullAdsSearchApi;

    @Override
    public GenericPageEventConverter.InputType getPageEventInputs(ModelAndView modelAndView) {
        if (gumtreePageContext.isInitialised()) {
            Object model = gumtreePageContext.getPageModel();
            AdvertData optionalAdvertData = null;

            if (model != null) {
                if (model instanceof Advert) {
                    optionalAdvertData =
                            toAdvertDataConverter.advertToAdvertData((Advert) model);
                }
            }

            return new GenericPageEventConverter.InputType(
                    gumtreePageContext.getPageType(),
                    hierarchyUtil.forCategory(gumtreePageContext.getCategory().getId()),
                    hierarchyUtil.forLocation(gumtreePageContext.getLocation().getId().longValue(), null, null, null, null),
                    optionalAdvertData);
        } else {
            return null;
        }
    }

    @Override
    public FullAdFlatAd loadDomainAdvert(Long advertId) {
        return fullAdsSearchApi.getAdvertById(advertId).toBlocking().value();
    }

    @Override
    public AdvertData convertToAdvert(FullAdFlatAd advert) {
        return toAdvertDataConverter.flatAdToAdvertData(advert);
    }

    @Override
    public HierarchicalData loadCategory(FullAdFlatAd advert) {
        return hierarchyUtil.forCategory(getPrimaryCategory(advert).getId());
    }

    @Override
    public HierarchicalData loadLocation(FullAdFlatAd advert) {
        return forLocation(advert);
    }

    public HierarchicalData forLocation(FullAdFlatAd flatAd) {
        FullAdLocation location = getPrimaryLocation(flatAd);

        String postcode = flatAd.getVisibleOnMap() ? flatAd.getPostcode() : null;
        String latlong = flatAd.getVisibleOnMap()
                ? flatAd.getCentroid().getLatitude() + ";" + flatAd.getCentroid().getLongitude()
                : null;

        return hierarchyUtil.forLocation(location.getId(), postcode, null, latlong, null);
    }

    static FullAdCategory getPrimaryCategory(FullAdFlatAd ad) {
        if (ad.getCategories() != null && !ad.getCategories().isEmpty()) {
            return ad.getCategories().stream().filter(FullAdCategory::getPrimary)
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("exactly one primary category is required"));
        } else {
            throw new IllegalArgumentException("at least one category is required");
        }
    }

    static FullAdLocation getPrimaryLocation(FullAdFlatAd ad) {
        if (ad.getLocations() != null && !ad.getLocations().isEmpty()) {
            return ad.getLocations().stream().filter(FullAdLocation::getPrimary)
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("exactly one primary location is required"));
        } else {
            throw new IllegalArgumentException("at least one location is required");
        }
    }
}
