package com.gumtree.web.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
public class SellerCorsFilter extends OncePerRequestFilter {

    private static final List<String> MATCHED_URL_PREFIXES = Arrays.asList(
            PostAdStatePath.MAPPING + "/",
            "/ajax/category/children",
            "/ajax/vrn",
            "/api/category/suggest",
            "/api/skill/",
            "/my-account/jobs/cv",
            "/my-account/jobs/cv/upload",
            "/my-account/jobs/cv/delete",
            "/my-account/jobs/cv/download",
            "/manage-account/update",
            "/manage-account/change-password",
            "/manage-account/subscribe",
            "/manage-account/contact-email/",
            "/manage-account/deactivate"
    );
    private static final List<String> ALLOWED_ORIGIN_SUFFIXES = Arrays.asList(
            ".gumtree.com",
            ".gumtree.io",
            ".gumtree.com:8080"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String origin = request.getHeader("Origin");

        if (
                origin != null
                        && MATCHED_URL_PREFIXES.stream().anyMatch(requestURI::startsWith)
                        && ALLOWED_ORIGIN_SUFFIXES.stream().anyMatch(origin::endsWith)
        ) {
            response.addHeader("Access-Control-Allow-Origin", origin);
            // {GET,POST} /postad/{id} has credentials
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.addHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, DELETE");
            response.addHeader("Access-Control-Allow-Headers",
                    "Content-Type,Authorization,X-Requested-With,experiments");
            response.addHeader("Access-Control-Max-Age", "3600");

            if (
                    request.getHeader("Access-Control-Request-Method") != null
                            && "OPTIONS".equals(request.getMethod())
            ) {
                response.setStatus(200);
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
}
