package com.gumtree.web.seller.page.payment.services;

import com.google.common.base.MoreObjects;
import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

@Service
public class PaymentSuccessService {
  private static final Logger LOG = LoggerFactory.getLogger(PaymentSuccessService.class);
  private static final String EMPTY_STRING = "";
  private static final String NONE = "none";
  private static final String URL_BASE = "/thankyou/";
  private static final String DELIMETER = ",";
  private static final String CARWOW_REDIRECT_TYPE = "carwow";

  private final CategoryService categoryService;

  @Autowired
  public PaymentSuccessService(CategoryService categoryService) {
    this.categoryService = categoryService;
  }

  public String getPaymentSuccessUrl(Checkout checkout) {
    String url;
    try {
      PageActionType actionType = checkout.getMetaPathInfo().getPageActionType();
      String popType = EMPTY_STRING;
      Long advertId = null;
      CheckoutAdvert advert = checkout.getAdvert();
      if (advert != null) {
        advertId = advert.getId();
      }
      if (actionType == PageActionType.POST) {
        if (advert != null) {
          String sellerType = MoreObjects.firstNonNull(advert.getSellerType(), StringUtils.EMPTY);
          long categoryId = advert.getCategoryId() != null ? advert.getCategoryId() : 0L;
          if ("private".equalsIgnoreCase(sellerType) && isCarsCategory(categoryId)) {
            popType = CARWOW_REDIRECT_TYPE;
          }
        }
      }

      UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getBaseURL(checkout));

      builder.queryParam("action", checkout.getMetaPathInfo().getPageActionType())
          .queryParam("payment", makeString(checkout.getMetaPathInfo().getPagePaymentTypes()))
          .queryParam("type", makeString(checkout.getMetaPathInfo().getFeatureTypes()))
          .queryParam("multiple", checkout.getMetaPathInfo().getIsMultipleAds() ? "yes" : "no")
          .queryParam("poptype", popType);
      if (advertId != null) {
        builder.queryParam("advertId", advertId);
      }
      return builder.build().toUri().toString();

    } catch (Exception e) {
      url = UriComponentsBuilder.fromUriString(getBaseURL(checkout)).build().toUri().toString();
    }
    return url;
  }

  private String getBaseURL(Checkout checkout) {
    return URL_BASE + checkout.getKey();
  }

  private String makeString(List<? extends Object> items) {
    if ((items == null) || items.isEmpty()) {
      return NONE;
    }

    String foldedItems = items.stream().map(x -> x.toString())
        .reduce(EMPTY_STRING, (a, b) -> a + b + DELIMETER);

    return foldedItems.endsWith(DELIMETER) ? foldedItems.substring(0, foldedItems.length() - 1) : foldedItems;
  }

  private boolean isCarsCategory(long categoryId) {
    Optional<Category> category = categoryService.getById(categoryId);
    if (!category.isPresent()) {
      return false;
    }
    return categoryService.getCategoriesList(category.get()).stream()
        .anyMatch(Categories.CARS::is);
  }
}
