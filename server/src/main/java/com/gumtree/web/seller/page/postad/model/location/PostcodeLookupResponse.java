package com.gumtree.web.seller.page.postad.model.location;

import java.util.Objects;

/**
 * Represents the response to looking up a postcode.
 */
public final class PostcodeLookupResponse {
    private PostcodeSelectionState state;
    private String postcode;
    private Long locationId;

    public PostcodeLookupResponse(PostcodeSelectionState state, String postcode) {
        this(state, postcode, null);
    }

    public PostcodeLookupResponse(
            PostcodeSelectionState state,
            String postcode,
            Long locationId) {

        this.state = state;
        this.postcode = postcode;
        this.locationId = locationId;
    }

    public PostcodeSelectionState getState() {
        return state;
    }

    public String getPostcode() {
        return postcode;
    }

    public Long getLocationId() {
        return locationId;
    }

    public boolean isOutcodeRecognised() {
        return state == PostcodeSelectionState.OUTCODE_RECOGNISED;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PostcodeLookupResponse that = (PostcodeLookupResponse) o;
        return state == that.state &&
                Objects.equals(postcode, that.postcode) &&
                Objects.equals(locationId, that.locationId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(state, postcode, locationId);
    }

    @Override
    public String toString() {
        return "PostcodeLookupResponse{" +
                "state=" + state +
                ", postcode='" + postcode + '\'' +
                ", locationId=" + locationId +
                '}';
    }
}
