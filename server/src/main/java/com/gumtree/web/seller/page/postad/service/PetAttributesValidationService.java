package com.gumtree.web.seller.page.postad.service;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeValueMetadata;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.Metric;
import com.gumtree.web.seller.page.postad.model.exception.BreedNotFoundException;
import com.gumtree.web.seller.page.postad.model.products.AnimalType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PetAttributesValidationService {

    private static final Logger LOG = LoggerFactory.getLogger(PetAttributesValidationService.class);

    private final CategoryModel categoryModel;
    private final CustomMetricRegistry customMetricRegistry;

    @Autowired
    public PetAttributesValidationService(CategoryModel categoryModel, CustomMetricRegistry customMetricRegistry) {
        this.categoryModel = categoryModel;
        this.customMetricRegistry = customMetricRegistry;
    }

    public void isValidBreedValuePassed(AnimalType animalType, Long categoryId, Map<String, String> attributes) {

        if (isPassedAttributeNotBlank(animalType, attributes)) {

            Optional<AttributeMetadata> attributeMetadata = categoryModel.findAttributesByNameForGivenCategory(
                    animalType.getBreedAttributeParentName(), categoryId);

            if (attributeMetadata.isPresent()) {
                List<AttributeValueMetadata> listOfBreeds = attributeMetadata.get().getValues();
                String providedBreedValue = attributes.get(animalType.getBreedAttributeParentName());
                isValidBreed(providedBreedValue, listOfBreeds);
            }
        } else {
            LOG.warn("Empty breed value has been passed");
            throw new BreedNotFoundException("Empty breed value has been passed");
        }
    }

    private static boolean isPassedAttributeNotBlank(AnimalType animalType, Map<String, String> attributes) {
        return attributes.containsKey(animalType.getBreedAttributeParentName())
                && StringUtils.isNotBlank(attributes.get(animalType.getBreedAttributeParentName()));
    }

    private void isValidBreed(String breedValue, List<AttributeValueMetadata> listOfBreeds) {
        if (!isBreedMatched(breedValue, listOfBreeds)) {
            LOG.warn("Unexpected breed value has been passed: {}", breedValue);
            throw new BreedNotFoundException("Unexpected breed value has been passed");
        }
    }

    private boolean isBreedMatched(String breedValue, List<AttributeValueMetadata> listOfBreeds) {
        return listOfBreeds.stream().anyMatch(attributeValueMetadata -> attributeValueMetadata.getValue().equalsIgnoreCase(breedValue));
    }
}
