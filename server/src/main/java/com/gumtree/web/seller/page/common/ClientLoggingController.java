package com.gumtree.web.seller.page.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

@Controller
public class ClientLoggingController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientLoggingController.class);

    @RequestMapping(value = "/client-error-logging",
            method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    public void updateUserDetails(@RequestParam("err") String err,
                                  @RequestParam("url") String url,
                                  @RequestParam("line") String line,
                                  @RequestParam("os") String os,
                                  @RequestParam("browser") String browser) {
        String msg = String.format("Js error: %s [%s | %s] [%s] %s", err, url, line, os, browser);
        LOGGER.error(msg);
    }
}
