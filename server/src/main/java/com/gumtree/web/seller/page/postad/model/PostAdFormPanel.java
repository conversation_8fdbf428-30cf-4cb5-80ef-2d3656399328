package com.gumtree.web.seller.page.postad.model;

public enum PostAdFormPanel {

    CATEGORY("category", false),
    LOCATION("location", false),
    AD_TITLE("ad-title", false),
    IMAGES("images", false),
    ADDITIONAL_FEATURES("additional-features", true),
    DESCRIPTION("description", false),
    WEBSITE_LINK("website-link", false),
    BUMP("bump", false),
    CONTACT_DETAILS("contact-details", false),
    CONTACT_DETAILS_PRO("contact-details-pro", false),
    OVERALL_PRICE("overall-price", false),
    PRICE("price", false),
    PETS_BIRTHDAY("pets-birthday", false),
    DOG_BREED("dogs-breed-panel", false),
    CAT_BREED("cats-breed-panel", false),
    PHONE_ATTRIBUTES("mobile-phones-attributes-panel", false),
    PET_HEALTH("pet-health-panel", false),
    ATTRIBUTE_PANEL("attribute-panel", false),
    VEHICLE_SPECIFICATIONS("vehicle-specifications", false),
    CONFIRMATION("confirmation", false),
    CONTINUE("continue", false),
    REGISTRATION("registration", false),
    LEGAL("legal", false),
    INSERTION("insertion", false),
    SELLER_TYPE("seller-type", false),
    AD_DETAILS("details", false),
    BRANDS("brands", false),
    DIY_TOOLS_MATERIALS_ATTRIBUTES("diy-tools-materials-attributes-panel", false);
    private final String id;
    private final boolean isAddedManually;

    PostAdFormPanel(String id, boolean isAddedManually) {
        this.id = id;
        this.isAddedManually = isAddedManually;
    }

    public String getId() {
        return id;
    }

    public boolean isAddedManually() {
        return isAddedManually;
    }
}
