package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.gumtree.api.Account;
import com.gumtree.api.AccountStatus;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.path.AbstractPath;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrix;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.shiro.RedirectUtils;
import com.gumtree.web.security.testing.SocialLoginUtil;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdModel;
import com.gumtree.web.seller.page.postad.model.path.ManageAdsPath;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.zeno.event.postad.EditAdBeginEvent;
import com.gumtree.web.zeno.event.postad.PostAdBeginEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.http.client.utils.URLEncodedUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Function;

/**
 * Controller for handling post ad page state redirects.
 */
@Controller
@RequestMapping(PostAdStatePath.MAPPING)
@GumtreePage(PageType.PostAd)
@ThreatMetrix
public final class PostAdStateController extends BasePostAdController {

    private static final String ATTRIBUTES_VRN_PARAM = "attributes[vrn]";

    private final PostAdWorkspace workspace;
    private final UserSecurityManager userSecurityManager;
    private final ZenoService zenoService;
    private final UserSession userSession;
    private final GumtreePageContext pageContext;

    private final CustomMetricRegistry metrics;

    /**
     * Constructor.
     *
     * @param userSession         the user session
     * @param categoryService     the category service
     * @param workspace           the post ad session workspace.
     * @param userSecurityManager the user security manager
     * @param zenoService         the zeno service
     * @param metrics
     */
    @Autowired
    public PostAdStateController(
            CookieResolver cookieResolver,
            CategoryModel categoryModel,
            ApiCallExecutor apiCallExecutor,
            ErrorMessageResolver messageResolver,
            UrlScheme urlScheme,
            PostAdWorkspace postAdWorkspace,
            UserSession userSession,
            CategoryService categoryService,
            PostAdWorkspace workspace,
            UserSecurityManager userSecurityManager,
            GumtreePageContext pageContext,
            LocationService locationService,
            ZenoService zenoService,
            UserSessionService userSessionService,
            CustomMetricRegistry metrics) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace, userSession,
                categoryService, pageContext, locationService, userSessionService);
        this.workspace = workspace;
        this.userSecurityManager = userSecurityManager;
        this.zenoService = zenoService;
        this.userSession = userSession;
        this.pageContext = pageContext;
        this.metrics = metrics;
    }

    /**
     * initial postad page - create editor based on advert/category id
     */
    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView createEditor(
            @RequestParam(value = "advertId", required = false) Long advertId,
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = "autoRepost", required = false, defaultValue = "false") Boolean autoRepost,
            RemoteIP remoteIP,
            HttpServletRequest request) {
        if (isAllowedToPost()) {
            PermanentCookie permanentCookie = cookieResolver.resolve(request, PermanentCookie.class);
            ThreatMetrixCookie threatMetrixCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
            java.util.Optional<String> vrm = RedirectUtils.getAndClearHttpPostPayload().flatMap(extractVrmParameter());
            AdvertEditor editor = metrics.postAdStatePageTimer("createAndPersistEditor").record(() ->
                    workspace.createAndPersistEditor(advertId, categoryId, vrm, remoteIP, permanentCookie,
                            threatMetrixCookie, true));
            sendZenoEvents(editor);

            String nextPath;

            if(autoRepost) {
                nextPath = GtProps.getStr("gumtree.host") + "/postad/bumpup?autoRepost=true&draftId=" + editor.getEditorId();
            } else {
                nextPath = new PostAdStatePath(editor.getEditorId()).getPath();
            }

            return redirect(nextPath);
        } else {
            return redirect(new ManageAdsPath().getPath());
        }
    }

    private Function<String, java.util.Optional<String>> extractVrmParameter() {
        return payload -> URLEncodedUtils.parse(payload, StandardCharsets.UTF_8).stream()
                .filter(queryParam -> queryParam.getName().equals(ATTRIBUTES_VRN_PARAM))
                .findFirst()
                .map(vrmParam -> vrmParam.getValue());
    }


    @RequestMapping(
            method = RequestMethod.POST,
            headers = {"content-type=application/x-www-form-urlencoded"},
            params = ATTRIBUTES_VRN_PARAM)
    public ModelAndView deepLinkToCarsEditor(
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = ATTRIBUTES_VRN_PARAM) String vrm,
            RemoteIP remoteIP,
            HttpServletRequest request) {

        if (isAllowedToPost()) {
            PermanentCookie permanentCookie = cookieResolver.resolve(request, PermanentCookie.class);
            ThreatMetrixCookie threatMetrixCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);

            AdvertEditor editor = workspace.createAndPersistEditor(
                    null, categoryId, java.util.Optional.ofNullable(vrm), remoteIP, permanentCookie,
                    threatMetrixCookie, false);

            sendZenoEvents(editor);

            return redirect(new PostAdStatePath(editor.getEditorId()).getPath());
        } else {

            return redirect(new ManageAdsPath().getPath());
        }
    }

    /**
     * base page without any data - form is loaded using javascript
     */
    @RequestMapping(value = PostAdStatePath.MAPPING_EDITOR, method = RequestMethod.GET)
    public ModelAndView showBasePage(@PathVariable("editorId") String editorId,
                                     HttpServletRequest request) {

        AdvertEditor editor = metrics.postAdStatePageTimer("getEditor").record(() -> workspace.getEditor(editorId));
        Long catId = editor.getCategoryId();

        List<Category> activeCategoryHierarchy = categoryModel.getHierarchy(catId);

        metrics.postAdStatePageTimer("refreshPreferredContactDetailsOfNonProUser")
                .record(editor::refreshPreferredContactDetailsOfNonProUser);

        PostAdModel.Builder postAdModelBuilder = PostAdModel.builder()
                .withEditor(editor, isSocial(request))
                .withDraftEnabled(editor.isCreateMode() && authenticatedUserSession.isLoggedIn());

        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request)
                .withUserType(authenticatedUserSession.isProUser())
                .withGaEventElements(editor.getAdvertDetail().getGaEvents())
                .withActiveCategory(catId)
                .withActiveCategoryHierarchy(activeCategoryHierarchy);

        CoreModel coreModel = buildCoreModelByPageType(editor, coreModelBuilder);
        ModelAndView pageModel = postAdModelBuilder.build(coreModel);

        // mark experimental to false after ftl has utilized it
        editor.getAdvertDetail().setDraft(false);

        // clean up ga once FE uses them (so we don't fire same event multiple times)
        editor.getAdvertDetail().setGaEvents(null);
        metrics.postAdStatePageTimer("updateEditor").record(() -> workspace.updateEditor(editorId, editor));

        return pageModel;
    }

    /**
     * form data used for initial request after base page is loaded
     */
    @RequestMapping(value = PostAdStatePath.MAPPING_EDITOR, method = RequestMethod.GET, params = "getFormData")
    @ResponseBody
    public PostAdFormBean getFormData(@PathVariable("editorId") String editorId) {
        return metrics.postAdStatePageTimer("getEditor").record(() -> workspace.getEditor(editorId)).getPostAdFormBean();
    }

    @Override
    protected String resolveMessage(String code, Object... args) {
        return getMessageResolver().getMessage(code, code, args);
    }

    /**
     * fallback for form being submitted on javascript fail
     * submitted values are omitted and the base page is reloaded
     */
    @RequestMapping(
            value = PostAdStatePath.MAPPING_EDITOR,
            method = RequestMethod.POST,
            headers = {"content-type=application/x-www-form-urlencoded"}
    )
    @ResponseBody
    public final ModelAndView postAdvert(@PathVariable("editorId") String editorId,
                                         HttpServletRequest request) {
        return showBasePage(editorId, request);
    }

    private Boolean isSocial(HttpServletRequest request) {
        return SocialLoginUtil.isRequiringMarketingOptIn(
                request, Optional.of(authenticatedUserSession.getUser()), true);
    }

    private boolean isAllowedToPost() {
        return !accountIsSuspended() && !userIsEbayMotorsUser();
    }

    private boolean accountIsSuspended() {
        if (getAuthenticatedUserSession().getSelectedAccountId() != null) {
            Account account = metrics.postAdStatePageTimer("userSecurityManager.getAccount").record(() ->
                    userSecurityManager.getAccount(getAuthenticatedUserSession().getSelectedAccountId(), getAuthenticatedUserSession()));
            if (account != null) {
                return !AccountStatus.ACTIVE.equals(account.getStatus());
            } else {
                //This will happen when password is null
                List<Account> accountList = metrics.postAdStatePageTimer("userSecurityManager.getUserAccounts").record(() ->
                        userSecurityManager.getUserAccounts(
                                getAuthenticatedUserSession().getSelectedAccountId().toString(), getAuthenticatedUserSession()));
                return !AccountStatus.ACTIVE.equals(accountList.get(0).getStatus());
            }
        }
        return false;
    }

    private boolean userIsEbayMotorsUser() {
        return authenticatedUserSession.getUser().isEbayMotorsUser();
    }

    private void sendZenoEvents(AdvertEditor editor) {
        if (editor.isCreateMode()) {
            zenoService.logEvent(editor, null, PostAdBeginEvent.class);
        } else {
            zenoService.logEvent(editor, null, EditAdBeginEvent.class);
        }
    }

    private CoreModel buildCoreModelByPageType(AdvertEditor editor, CoreModel.Builder coreModel) {
        Page page;
        String title;
        if (editor.isCreateMode()) {
            title = "Post an ad | Gumtree.com";
            page = Page.PostAd;
        } else {
            page = Page.EditAd;
            title = "Edit your ad | Gumtree.com";
        }
        coreModel.withTitle(title);
        pageContext.setPageType(page.getPageType());
        return coreModel.build(page);
    }
}
