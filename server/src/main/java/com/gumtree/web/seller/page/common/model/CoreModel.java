package com.gumtree.web.seller.page.common.model;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.UserType;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.CommonProperty;
import com.gumtree.global.AppViewDevice;
import com.gumtree.global.PageTheme;
import com.gumtree.mobile.web.seo.metadata.SEOMetadata;
import com.gumtree.util.model.Actions;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.browse.BrowseCategoriesLocationResolvingService;
import com.gumtree.web.browse.BrowseCategoriesService;
import com.gumtree.web.browse.ResponsiveBrowseModel;
import com.gumtree.web.common.domain.location.ResolvedLocation;
import com.gumtree.web.common.domain.user.User;
import com.gumtree.web.common.serializer.CategoryListSerializer;
import com.gumtree.web.common.serializer.CategorySerializerLegacy;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookie;
import com.gumtree.web.cookie.cutters.session.SessionCookie;
import com.gumtree.web.cookie.cutters.userpreferences.UserPreferencesCookie;
import com.gumtree.web.feature.FeatureSwitch;
import com.gumtree.web.feature.FeatureSwitchManager;
import com.gumtree.web.jobs.JobsConfig;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.zeno.core.domain.PageType;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CoreModel {

    private final String backUrl;
    private final String myGumtreeHost;
    private final User user;
    private final String loginUrl;
    private final String logoutUrl;
    private final Category category;
    private final List<Category> categoryFilters;
    private final boolean clientLogging;
    private final boolean noIndex;
    private final boolean footerBanner;
    private final Page page;
    private final String currentUrl;
    private final String host;
    private final String searchLocation;
    private final UserPreferencesCookie userPreferencesCookie;
    private final SEOMetadata seoMetadata;
    private final String h1;
    private final String metaDescription;
    private final String title;
    private final List<String> features;
    private final Map<String, String> experiments;
    private final List<String> gaEvents;
    private final List<GaElement> gaEventElements;
    private final boolean messageCentreEnabled;
    private final boolean messageCentreLinkEnabled;
    private final String messagesLink;
    private final boolean brandRefreshEnabled;
    private final Integer headerNotificationPollingFrequencyInMinutes;
    private final String env;
    private final AppBannerCookie appBannerCookie;
    private final UserType userType;
    private final JobsConfig jobsConfig;
    private final ResponsiveBrowseModel responsiveBrowseModel;

    private final Long activeCategory;
    private List<Category> activeCategoryHierarchy;

    private AppViewDevice appViewDevice;

    private PageTheme pageTheme;

    public Long getActiveCategory() {
        return activeCategory;
    }

    public List<Category> getActiveCategoryHierarchy() {
        return activeCategoryHierarchy;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public String getMyGumtreeHost() {
        return myGumtreeHost;
    }

    public User getUser() {
        return user;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public String getLogoutUrl() {
        return logoutUrl;
    }

    @JsonSerialize(using = CategoryListSerializer.class)
    public List<Category> getCategoryFilter() {
        return categoryFilters;
    }

    public boolean isClientLogging() {
        return clientLogging;
    }

    public boolean isNoIndex() {
        return noIndex;
    }

    public boolean isFooterBanner() {
        return footerBanner;
    }

    public int getCurrentYear() {
        return DateTime.now().getYear();
    }

    public Page getPage() {
        return page;
    }

    public PageType getPageType() {
        return page.getPageType();
    }

    public String getCurrentUrl() {
        return currentUrl;
    }

    public String getHost() {
        return host;
    }

    public String getSearchLocation() {
        return searchLocation;
    }

    public UserPreferencesCookie getUserPreferences() {
        return userPreferencesCookie;
    }

    public String getMetaDescription() {
        return metaDescription;
    }

    public String getTitle() {
        return title;
    }

    public String getH1() {
        return h1;
    }

    public SEOMetadata getSeoMetadata() {
        return seoMetadata;
    }

    @JsonSerialize(using = CategorySerializerLegacy.class)
    public Category getCategory() {
        return category;
    }

    public  List<String> getFeatures() {
        return features;
    }

    public Map<String, String> getExperiments() {
        return experiments;
    }

    public List<String> getGaEvents() {
        return gaEvents;
    }

    public List<GaElement> getGaEventElements() {
        return gaEventElements;
    }

    public boolean isMessageCentreEnabled() {
        return messageCentreEnabled;
    }

    public boolean isBrandRefreshEnabled() {
        return brandRefreshEnabled;
    }

    public String getMessagesLink() {
        return messagesLink;
    }

    public UserType getUserType() {
        return userType;
    }

    public int getHeaderNotificationPollingFrequencyInMinutes() {
        return headerNotificationPollingFrequencyInMinutes;
    }

    public AppBannerCookie getAppBannerCookie() {
        return appBannerCookie;
    }

    public String getEnv() {
        return env;
    }

    public JobsConfig getJobsConfig() {
        return jobsConfig;
    }

    public ResponsiveBrowseModel getResponsiveBrowseModel() {
        return responsiveBrowseModel;
    }

    public AppViewDevice getAppViewDevice() { return appViewDevice; }

    public PageTheme getPageTheme() { return pageTheme; }

    protected CoreModel(Builder builder) {
        this.clientLogging = builder.clientLogging;
        this.searchLocation = builder.searchLocation;
        this.noIndex = builder.noIndex;
        this.footerBanner = builder.footerBanner;
        this.category = builder.category;
        this.categoryFilters = builder.categoryFilter;
        this.page = builder.page;
        this.backUrl = builder.backUrl;
        this.userPreferencesCookie = builder.userPreferencesCookie;
        this.currentUrl = builder.currentUrl;
        this.user = builder.user;
        this.host = builder.host;
        this.myGumtreeHost = builder.myGumtreeHost;
        this.loginUrl = builder.loginUrl;
        this.logoutUrl = builder.logoutUrl;
        this.seoMetadata = builder.seoMetadata;
        this.h1 = builder.seoMetadata != null ? builder.seoMetadata.getH1() : "";
        this.metaDescription = builder.seoMetadata != null ? builder.seoMetadata.getDescription() : "";
        this.title = getTitle(builder);
        this.features = builder.features;
        this.experiments = builder.experiments;
        this.gaEvents = builder.gaEvents;
        this.gaEventElements = builder.gaEventElements;
        this.messageCentreEnabled = builder.messageCentreEnabled;
        this.messageCentreLinkEnabled = builder.messageCentreLinkEnabled;
        this.messagesLink = builder.messagesLink;
        this.brandRefreshEnabled = builder.brandRefreshEnabled;
        this.headerNotificationPollingFrequencyInMinutes = builder.headerNotificationPollingFrequencyInMinutes;
        this.appBannerCookie = builder.appBannerCookie;
        this.env = builder.env.name();
        this.userType = builder.userType;
        this.jobsConfig = builder.jobsConfig;
        this.responsiveBrowseModel = builder.responsiveBrowseModel;
        this.activeCategory = builder.activeCategory;
        this.activeCategoryHierarchy = builder.activeCategoryHierarchy;
        this.appViewDevice = builder.appViewDevice;
        this.pageTheme = builder.pageTheme;
    }


    private String getTitle(Builder builder) {
        String seoTitle = builder.seoMetadata != null ? builder.seoMetadata.getTitle() : null;
        return isNotEmpty(seoTitle) ? seoTitle : builder.title;
    }

    private boolean isNotEmpty(String text) {
        return text != null && !text.trim().isEmpty();
    }

    public static Builder builder(Env env, CategoryModel categoryModel, CookieResolver cookieResolver) {
        return new Builder(env, categoryModel, cookieResolver);
    }

    public boolean isMessageCentreLinkEnabled() {
        return messageCentreLinkEnabled;
    }

    public static final class Builder {
        private boolean clientLogging;
        private String searchLocation;
        // by default most pages use noIndex tag, so we set this by default
        private boolean noIndex = true;
        private boolean footerBanner;
        private Category category;
        private List<Category> categoryFilter = Lists.newArrayList();
        private Page page;
        private String backUrl;
        private SessionCookie sessionCookie;
        private UserPreferencesCookie userPreferencesCookie;
        private User user;
        private String currentUrl;
        private String host;
        private String myGumtreeHost;
        private String loginUrl;
        private String logoutUrl;
        private SEOMetadata seoMetadata;
        private String title;
        private CategoryModel categoryModel;
        private List<String> features = new ArrayList<>();
        private Map<String, String> experiments = new HashMap<>();
        private List<String> gaEvents = Lists.newArrayList();
        private List<GaElement> gaEventElements = Lists.newArrayList();
        private boolean messageCentreEnabled;
        private boolean messageCentreLinkEnabled;
        private Integer headerNotificationPollingFrequencyInMinutes;

        private String messagesLink;
        private boolean brandRefreshEnabled;
        private final CookieResolver cookieResolver;
        private AppBannerCookie appBannerCookie;
        private final Env env;
        private UserType userType;
        private JobsConfig jobsConfig;
        private ResponsiveBrowseModel responsiveBrowseModel;
        private Long activeCategory;
        private List<Category> activeCategoryHierarchy;

        private AppViewDevice appViewDevice;

        private PageTheme pageTheme = PageTheme.WEBVIEW;

        private Builder(@Nonnull Env env, CategoryModel categoryModel, CookieResolver cookieResolver) {
            Assert.notNull(env);

            this.env = env;
            this.categoryModel = categoryModel;
            this.cookieResolver = cookieResolver;
        }

        public Builder withSearchLocation(String searchLocation) {
            if (!com.gumtree.domain.location.Location.UK_DISPLAY_NAME.equals(searchLocation)) {
                this.searchLocation = searchLocation;
            }
            return this;
        }

        public Builder withActiveCategory(Long activeCategory) {
            this.activeCategory = activeCategory;
            return this;
        }

        public Builder withActiveCategoryHierarchy(List<Category> activeCategoryHierarchy) {
            this.activeCategoryHierarchy = activeCategoryHierarchy;
            return this;
        }

        public Builder withCategory(Category category) {
            this.category = category;
            categoryFilter = getCategoryFilters(category);
            return this;
        }

        public Builder withIndex() {
            this.noIndex = false;
            return this;
        }

        public Builder withFooterBanner() {
            this.footerBanner = true;
            return this;
        }

        public Builder withBackUrl(String backUrl) {
            this.backUrl = backUrl;
            return this;
        }

        public Builder withUserPreferences(HttpServletRequest request) {
            this.userPreferencesCookie = cookieResolver.resolve(request, UserPreferencesCookie.class);
            return this;
        }

        public Builder withSessionCookie(HttpServletRequest request) {
            this.sessionCookie = cookieResolver.resolve(request, SessionCookie.class);
            return this;
        }

        public Builder withUser(Optional<com.gumtree.api.User> userOpt){
            User.Builder builder = new User.Builder().withUserLoggedIn(userOpt.isPresent());
            if (userOpt.isPresent()) {
                com.gumtree.api.User user = userOpt.get();
                builder.withEmail(user.getEmail())
                        .withFirstName(user.getFirstName())
                        .withId(user.getId());
            }
            this.user = builder.build();

            return this;
        }

        public Builder withUser(Optional<com.gumtree.api.User> userOpt, CSRFTokenService csrfTokenService) {
            User.Builder builder = new User.Builder().withUserLoggedIn(userOpt.isPresent());
            if (userOpt.isPresent()) {
                com.gumtree.api.User user = userOpt.get();
                builder.withEmail(user.getEmail())
                        .withFirstName(user.getFirstName())
                        .withCsrfToken(csrfTokenService.csrfTokenForEmail(user.getEmail()))
                        .withId(user.getId());

                if(user.getCreationDate() != null) {
                    builder.withCreationDateMillis(user.getCreationDate().getMillis());
                }
            }
            this.user = builder.build();

            return this;
        }

        public Builder withCurrentUrl(String currentUrl) {
            this.currentUrl = currentUrl;
            return this;
        }

        public Builder withLoginUrl(String loginUrl) {
            this.loginUrl = loginUrl;
            return this;
        }

        public Builder withLogoutUrl(String logoutUrl) {
            this.logoutUrl = logoutUrl;
            return this;
        }

        public Builder withClientLogEnabled(boolean clientLogging) {
            this.clientLogging = clientLogging;
            return this;
        }

        public Builder withMyGumtreeHost(String myGumtreeHost) {
            this.myGumtreeHost = myGumtreeHost;
            return this;
        }

        public Builder withHost(String host) {
            this.host = host;
            return this;
        }

        public Builder withSeoMetadata(SEOMetadata seoMetadata) {
            this.seoMetadata = seoMetadata;
            return this;
        }

        public Builder withTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder withMessageCentreEnabled(Boolean messageCentreEnabled, Boolean messageCentreLinkEnabled) {
            this.messageCentreEnabled = isMessageCentreEnabled(messageCentreEnabled, messageCentreLinkEnabled);
            return this;
        }

        Boolean isMessageCentreEnabled(Boolean messageCentreEnabled, Boolean messageCentreLinkEnabled) {
            return messageCentreEnabled && messageCentreLinkEnabled;
        }

        public Builder withMessageCentreLinkEnabled(boolean messageCentreLinkEnabled) {
            this.messageCentreLinkEnabled = messageCentreLinkEnabled;
            return this;
        }

        public Builder withMessagesLink(String messagesLink) {
            this.messagesLink = messagesLink;
            return this;
        }

        public Builder withUserType(boolean isPro) {
            userType = isPro ? UserType.PRO : UserType.STANDARD;

            return this;
        }

        public Builder withBrandRefreshEnabled(boolean brandRefreshEnabled) {
            this.brandRefreshEnabled = brandRefreshEnabled;
            return this;
        }

        public Builder withExperiments(Experiments experiments) {
            experiments.getExperiments().forEach((name, value) -> this.experiments.put(name, value));
            return this;
        }

        public Builder withFeatures(List<FeatureSwitch> enabledFeatures) {
            this.features = enabledFeatures.stream().map(FeatureSwitch::name).collect(Collectors.toList());
            return this;
        }

        public Builder withGaEvents(List<String> gaEvents) {
            this.gaEvents = gaEvents;
            return this;
        }

        public Builder withGaEventElements(List<GaElement> gaEventElements) {
            this.gaEventElements = gaEventElements;
            return this;
        }

        public Builder withHeaderNotificationPollingFrequencyInMinutes(Integer pollingFrequencyInMinutes) {
            Assert.notNull(pollingFrequencyInMinutes);
            this.headerNotificationPollingFrequencyInMinutes = pollingFrequencyInMinutes;
            return this;
        }

        public Builder withJobsConfig(JobsConfig jobsConfig) {
            this.jobsConfig = jobsConfig;
            return this;
        }

        public Builder withBrowseModel(ResponsiveBrowseModel responsiveBrowseModel) {
            this.responsiveBrowseModel = responsiveBrowseModel;
            return this;
        }

        public Builder withAppViewDeviceAndPageTheme(HttpServletRequest request) {
            String userAgent = request.getHeader("user-agent");

            if(userAgent != null) {
                if(userAgent.contains("iOS/GumtreeApp")) {
                    this.appViewDevice = AppViewDevice.IOS;
                } else if(userAgent.contains("Android/GumtreeApp")) {
                    this.appViewDevice = AppViewDevice.ANDROID;
                }
            }

            if(this.appViewDevice != null) {
                this.pageTheme = PageTheme.APPVIEW;
            }

            return this;
        }

        public CoreModel build(Page page) {
            this.page = page;
            if (categoryFilter.isEmpty()) {
                Category root = categoryModel.getRootCategory();
                withCategory(root);
            }

            if (searchLocation == null) {
                searchLocation = "";
            }

            return new CoreModel(this);
        }


        private List<Category> getCategoryFilters(final Category category) {
            final Long categoryId = category.getId();
            List<Category> fullPath = Lists.newArrayList(categoryModel.getFullPath(categoryId));
            fullPath.addAll(category.getChildren());
            return fullPath;
        }

        public Builder withAppBannerCookie(AppBannerCookie appBannerCookie) {
            this.appBannerCookie = appBannerCookie;
            return this;
        }
    }

    public static class BuilderProps {
        private final Env env;
        private final String host;
        private final String myGumtreeHost;
        private final boolean clientLogging;
        private final PropSupplier<Boolean> messageCentreEnabled;
        private final PropSupplier<Boolean> messageCentreLinkEnabled;
        private final PropSupplier<Integer> headerNotificationPollingFrequency;

        public BuilderProps(Env env,
                            String host,
                            String myGumtreeHost,
                            boolean clientLogging,
                            PropSupplier<Boolean> messageCentreEnabled,
                            PropSupplier<Boolean> messageCentreLinkEnabled,
                            PropSupplier<Integer> headerNotificationPollingFrequency) {
            this.env = env;
            this.host = host;
            this.myGumtreeHost = myGumtreeHost;
            this.clientLogging = clientLogging;
            this.messageCentreEnabled = messageCentreEnabled;
            this.messageCentreLinkEnabled = messageCentreLinkEnabled;
            this.headerNotificationPollingFrequency = headerNotificationPollingFrequency;
        }

        public String getHost() {
            return host;
        }

        public String getMyGumtreeHost() {
            return myGumtreeHost;
        }

        public boolean isClientLogging() {
            return clientLogging;
        }

        public PropSupplier<Boolean> getMessageCentreEnabled() {
            return messageCentreEnabled;
        }

        public PropSupplier<Boolean> getMessageCentreLinkEnabled() {
            return messageCentreLinkEnabled;
        }

        public PropSupplier<Integer> getHeaderNotificationPollingFrequency() {
            return headerNotificationPollingFrequency;
        }

        public Env getEnv() {
            return env;
        }
    }

    public static class BuilderFactory {
        private static final String LOGOUT_URL = "/logout";
        private static final String LOGIN_URL = "/login";

        // props
        private final BuilderProps props;

        // deps
        private final ExperimentsProvider experimentsProvider;
        private final CategoryModel categoryModel;
        private final CookieResolver cookieResolver;
        private final UserSessionService userSessionService;
        private final BrowseCategoriesService browseCategoriesService;
        private final BrowseCategoriesLocationResolvingService browseCategoriesLocationResolvingService;
        private final FeatureSwitchManager featureSwitchManager;
        private final CSRFTokenService csrfTokenService;

        public BuilderFactory(BuilderProps props,
                              CategoryModel categoryModel,
                              CookieResolver cookieResolver,
                              UserSessionService userSessionService,
                              ExperimentsProvider experimentsProvider,
                              BrowseCategoriesService browseCategoriesService,
                              BrowseCategoriesLocationResolvingService browseCategoriesLocationResolvingService,
                              FeatureSwitchManager featureSwitchManager,
                              CSRFTokenService csrfTokenService) {
            this.props = props;
            this.experimentsProvider = experimentsProvider;
            this.categoryModel = categoryModel;
            this.cookieResolver = cookieResolver;
            this.userSessionService = userSessionService;

            this.browseCategoriesService = browseCategoriesService;
            this.browseCategoriesLocationResolvingService = browseCategoriesLocationResolvingService;
            this.featureSwitchManager = featureSwitchManager;
            this.csrfTokenService = csrfTokenService;
        }

        public Builder create(@Nonnull HttpServletRequest request){
            return create(request, categoryModel.getRootCategory());
        }

        public Builder create(@Nonnull HttpServletRequest request, @Nonnull Category category){
            Experiments experiments = experimentsProvider.get();
            UserPreferencesCookie userPreferencesCookie = cookieResolver.resolve(request, UserPreferencesCookie.class);
            ResolvedLocation resolvedLocation = browseCategoriesLocationResolvingService.locallyResolveLocation(userPreferencesCookie);


            CoreModel.Builder builder = CoreModel.builder(props.getEnv(), categoryModel, cookieResolver);


            return builder
                    .withCategory(category)
                    .withSearchLocation(resolvedLocation.getSearchLocation())
                    .withClientLogEnabled(props.clientLogging)
                    .withSessionCookie(request)
                    .withUserPreferences(request)
                    .withAppBannerCookie(cookieResolver.resolve(request, AppBannerCookie.class))
                    .withUser(userSessionService.getUser(),csrfTokenService)
                    .withCurrentUrl(getFullURL(request))
                    .withHost(props.host)
                    .withMyGumtreeHost(props.myGumtreeHost)
                    .withLoginUrl(LOGIN_URL)
                    .withLogoutUrl(LOGOUT_URL)
                    .withFeatures(featureSwitchManager.getAllEnabledFeatures())
                    .withExperiments(experiments)
                    .withMessageCentreEnabled(props.messageCentreEnabled.get(), props.messageCentreLinkEnabled.get())
                    .withMessageCentreLinkEnabled(props.messageCentreLinkEnabled.get())
                    .withMessagesLink(props.myGumtreeHost + Actions.BUSHFIRE_MESSAGE_CENTRE.getUrl())
                    .withHeaderNotificationPollingFrequencyInMinutes(props.headerNotificationPollingFrequency.get())
                    .withJobsConfig(
                            new JobsConfig(
                                    GtProps.getStr(CommonProperty.MADGEX_WEB_URL),
                                    GtProps.getStr(CommonProperty.MADGEX_WEB_POSTAD_URL)
                            )
                    )
                    .withBrowseModel(browseCategoriesService.getHomePageCategoriesModel(resolvedLocation))
                    .withAppViewDeviceAndPageTheme(request);
        }

        private String getFullURL(@Nonnull HttpServletRequest request) {
            StringBuilder requestURL = new StringBuilder(props.myGumtreeHost).append(request.getRequestURI());
            String queryString = request.getQueryString();

            if (!StringUtils.isEmpty(queryString)) {
                requestURL.append('?').append(queryString);
            }
            return requestURL.toString();
        }
    }
}
