package com.gumtree.web.seller.page.postad.controller.steps;

import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import org.springframework.core.Ordered;

/**
 * Step to execute when an user is posting an ad
 */
public interface PostAdStep extends Ordered {

    /**
     * Execute the step
     * @param model the model to build
     * @param editor the ad editor to use
     * @return {@code true} if the step was processed successfully and the next steps in the chain should be executed
     */
    boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor);
}
