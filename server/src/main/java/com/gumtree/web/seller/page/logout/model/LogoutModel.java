package com.gumtree.web.seller.page.logout.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public final class LogoutModel extends CommonModel {

    private LogoutModel(CoreModel core, Builder builder) {
        super(core);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.Logout;
            coreModelBuilder.withTitle("Log out");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new LogoutModel(coreModelBuilder.build(page), this));
        }

    }
}
