package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import org.springframework.web.servlet.ModelAndView;

public final class PostAdModel extends CommonModel {

    private final String editorId;
    private final String title;
    private final boolean enableMarketingOptIn;
    private final boolean draftEnabled;
    private final boolean currentPostAdADraft;

    private PostAdModel(CoreModel core, Builder builder) {
        super(core);
        this.editorId = builder.editor.getEditorId();
        this.title = builder.editor.getPostAdFormBean().getTitle();
        this.enableMarketingOptIn = builder.enableMarketingOptIn;
        this.draftEnabled = builder.draftEnabled;
        this.currentPostAdADraft = builder.editor.getAdvertDetail().isDraft();
    }

    public String getEditorId() {
        return editorId;
    }

    public String getTitle() {
        return title;
    }

    public boolean isEnableMarketingOptIn() {
        return enableMarketingOptIn;
    }

    public boolean getDraftEnabled() {
        return draftEnabled;
    }

    public boolean getCurrentPostAdADraft() {
        return currentPostAdADraft;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private AdvertEditor editor;
        private boolean enableMarketingOptIn;
        private boolean draftEnabled;

        public Builder withDraftEnabled(boolean draftEnabled){
            this.draftEnabled = draftEnabled;
            return this;
        }

        public Builder withEditor(AdvertEditor editor, boolean enableMarketingOptIn){
            this.editor = editor;
            this.enableMarketingOptIn = enableMarketingOptIn;
            return this;
        }

        public ModelAndView build(CoreModel coreModel) {
            return new ModelAndView(coreModel.getPage().getTemplateName(), CommonModel.MODEL_KEY,
                    new PostAdModel(coreModel, this));
        }
    }
}
