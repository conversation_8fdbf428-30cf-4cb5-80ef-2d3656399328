package com.gumtree.web.seller.page.postad.api;

import com.gumtree.api.Ad;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * API call to post an advert via the Bushfire API.
 */
public final class PostAdApiCall extends AuthenticatedApiCall<Ad> {

    private PostAdvertBean postAdvertBean;

    /**
     * Constructor.
     * @param postAdvertBean the bean to post
     * @param apiKeyProvider the key provider to use to sign the request
     */
    public PostAdApiCall(PostAdvertBean postAdvertBean, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.postAdvertBean = postAdvertBean;
    }

    @Override
    public Ad execute(BushfireApi api) {
        return api.create(AdvertApi.class, getApiKey()).postAd(postAdvertBean);
    }

    public PostAdvertBean getPostAdvertBean() {
        return postAdvertBean;
    }
}
