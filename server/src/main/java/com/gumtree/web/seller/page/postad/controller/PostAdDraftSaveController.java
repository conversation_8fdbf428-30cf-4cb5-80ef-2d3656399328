package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.service.PostAdWorkspace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Saves draft when user changes value of field on SYI form
 * Saving happens in background
 */
@Controller
@RequestMapping("/postad-draft")
public class PostAdDraftSaveController extends BasePostAdController {

    @Autowired
    public PostAdDraftSaveController(
            CookieResolver cookieResolver,
            CategoryModel categoryModel,
            ApiCallExecutor apiCallExecutor,
            ErrorMessageResolver messageResolver,
            UrlScheme urlScheme,
            PostAdWorkspace postAdWorkspace,
            UserSession userSession,
            CategoryService categoryService,
            GumtreePageContext pageContext,
            LocationService locationService,
            UserSessionService userSessionService
    ) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace, userSession,
                categoryService, pageContext, locationService, userSessionService);
    }

    /**
     * main submit method
     */
    @RequestMapping(
            value = PostAdStatePath.MAPPING_EDITOR,
            method = RequestMethod.POST,
            headers = {"content-type=application/json"}
    )
    @ResponseStatus(value = HttpStatus.OK)
    public final void postAdvert(
            @RequestBody PostAdFormBean postAdFormBean,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId) {

        AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
        editor.setPostAdFormBean(postAdFormBean);
        getPostAdWorkspace().updateEditor(editorId, editor);
    }
}
