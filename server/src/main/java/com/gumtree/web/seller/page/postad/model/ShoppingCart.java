package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.seller.domain.product.entity.ProductName;

/**
 * Represents a simple cart that products can be added to.
 */
public interface ShoppingCart {

    /**
     * Add a product.
     *
     * @param productName the product
     */
    void addProduct(ProductName productName);

    /**
     * @return this cart converted to an API {@link CreateOrderBean} model.
     */
    CreateOrderBean toOrderBean();
}
