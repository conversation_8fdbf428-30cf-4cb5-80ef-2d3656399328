package com.gumtree.web.seller.page.postad.model;


import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;

import static com.gumtree.web.seller.page.common.model.Page.PaymentCheckoutPage;


public final class PaymentCheckoutModel extends CommonModel {
    private String checkoutFormAction;
    private Order order;
    private String checkoutKey;
    private Object clientBraintreeToken;
    private String braintreeEnvironment;
    private String merchantId;
    private BigDecimal checkoutAmount;
    private boolean paymentFormEnabled;
    private boolean showDropin;

    public String getCheckoutFormAction() {
        return checkoutFormAction;
    }

    public Order getOrder() {
        return order;
    }

    public String getCheckoutKey() {
        return checkoutKey;
    }

    public Object getClientBraintreeToken() {
        return clientBraintreeToken;
    }

    public String getBraintreeEnvironment() {
        return braintreeEnvironment;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public BigDecimal getCheckoutAmount() {
        return checkoutAmount;
    }

    public boolean isPaymentFormEnabled() {
        return paymentFormEnabled;
    }

    public PaymentCheckoutModel(CoreModel.Builder coreBuilder) {
        super(coreBuilder.build(PaymentCheckoutPage));
    }

    private PaymentCheckoutModel(CoreModel core) {
        super(core);
    }

    public boolean isShowDropin() {
        return showDropin;
    }

    public static Builder builder(CoreModel.Builder coreBuilder) {
        return new Builder(coreBuilder);
    }

    public static final class Builder {
        private PaymentCheckoutModel model;

        private Builder(CoreModel.Builder coreBuilder) {
            model = new PaymentCheckoutModel(coreBuilder.build(PaymentCheckoutPage));
        }

        public Builder withCheckoutFormAction(String checkoutFormAction) {
            model.checkoutFormAction = checkoutFormAction;
            return this;
        }

        public Builder withOrder(Order order) {
            model.order = order;
            return this;
        }

        public Builder withCheckoutKey(String checkoutKey) {
            model.checkoutKey = checkoutKey;
            return this;
        }

        public Builder withClientBraintreeToken(Object clientBraintreeToken) {
            model.clientBraintreeToken = clientBraintreeToken;
            return this;
        }

        public Builder withBraintreeEnvironment(String braintreeEnvironment) {
            model.braintreeEnvironment = braintreeEnvironment;
            return this;
        }

        public Builder withMerchantId(String merchantId) {
            model.merchantId = merchantId;
            return this;
        }

        public Builder withShowDropin(boolean showDropin) {
            model.showDropin = showDropin;
            return this;
        }

        public Builder withCheckoutAmount(BigDecimal checkoutAmount) {
            model.checkoutAmount = checkoutAmount;
            return this;
        }

        public Builder withPaymentFormEnabled(boolean paymentFormEnabled) {
            model.paymentFormEnabled = paymentFormEnabled;
            return this;
        }

        public ModelAndView build() {
            ModelAndView modelAndView = new ModelAndView(model.getCore().getPage().getTemplateName());
            modelAndView.addObject(CommonModel.MODEL_KEY, model);
            return modelAndView;
        }
    }
}