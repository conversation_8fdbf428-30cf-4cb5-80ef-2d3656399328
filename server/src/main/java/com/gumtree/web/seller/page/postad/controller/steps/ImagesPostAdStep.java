package com.gumtree.web.seller.page.postad.controller.steps;

import com.gumtree.api.Image;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormStatus;
import com.gumtree.web.seller.page.postad.model.PostAdImage;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.PostAdWorkspace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService.HintSection.IMAGES;

@Component
public class ImagesPostAdStep implements PostAdStep {
    public static final Integer ORDER = RateLimiterPostAdStep.ORDER + 1;

    @Autowired
    private PostAdWorkspace postAdWorkspace;

    @Autowired
    private PostAdImageConverter postAdImageConverter;

    @Autowired
    private PostAdFormDescriptionHintService descriptionHintService;

    @Autowired
    private CategoryService categoryService;

    @Value("#{'${gumtree.postad.validation.image.categories:2526,10442}'.split(',')}")
    private Long[] categoriesWithImageRequired;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        model.withStatus(PostAdFormStatus.FORM);

        postAdWorkspace.updateEditor(editor.getEditorId(), editor);

        model.withImages(convertImages(editor.getImages()));
        model.withImagesHint(
                descriptionHintService.getHint(IMAGES, editor.getCategoryId(), editor.getLocationId()));
        return true;
    }

    private List<PostAdImage> convertImages(List<Image> images) {
        return images.stream().map(postAdImageConverter::convertImageToPostAdImage).collect(Collectors.toList());
    }




}
