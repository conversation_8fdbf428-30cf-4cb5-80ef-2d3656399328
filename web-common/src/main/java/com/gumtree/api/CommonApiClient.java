package com.gumtree.api;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import feign.auth.BasicAuthRequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.codec.ErrorDecoder;
import feign.hystrix.HystrixFeign;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;

import java.util.Optional;
import java.util.function.Consumer;

public class CommonApiClient {

    public interface Api {
    }

    private final ObjectMapper objectMapper;
    private final String basePath;
    private final HystrixFeign.Builder feignBuilder;
    private final String commandGroupName;
    private final int poolSize;

    /**
     * Creates client to be used for internal services. 'User-Agent' header is added by default
     */
    public CommonApiClient(String basePath, String clientId, int connectionTimeOut, int readTimeOut,
                           String commandGroupName, int poolSize) {
        this(basePath, connectionTimeOut, readTimeOut, commandGroupName, poolSize, new OkHttpClient());
        feignBuilder.requestInterceptor(new ClientIdRequestInterceptor(clientId));
    }

    /**
     * Creates client to be used for internal services. 'User-Agent' header is added by default
     */
    public CommonApiClient(String basePath, String clientId, int connectionTimeOut, int readTimeOut,
                           String commandGroupName, int poolSize, OkHttpClient client) {
        this(basePath, connectionTimeOut, readTimeOut, commandGroupName, poolSize, client);
        feignBuilder.requestInterceptor(new ClientIdRequestInterceptor(clientId));
    }

    public CommonApiClient(String basePath, int connectionTimeOut, int readTimeOut, String commandGroupName, int poolSize,
                           OkHttpClient client) {
        this.commandGroupName = commandGroupName;
        this.poolSize = poolSize;
        this.basePath = basePath;
        objectMapper = createObjectMapper();
        feignBuilder = HystrixFeign.builder()
                .client(client)
                .decoder(new JacksonDecoder(objectMapper))
                .encoder(new JacksonEncoder(objectMapper))
                .options(new Request.Options(connectionTimeOut, readTimeOut));
    }

    private ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        return objectMapper;
    }

    private static class ClientIdRequestInterceptor implements RequestInterceptor {
        private final String clientId;

        public ClientIdRequestInterceptor(String clientId) {
            this.clientId = clientId;
        }

        @Override
        public void apply(RequestTemplate template) {
            template.header("User-Agent", clientId);
        }
    }

    public <T> Builder<T> builder(Class<T> clientClass) {
        return new Builder<>(clientClass);
    }

    public class Builder<T> {

        private final Class<T> clientClass;

        private T nullableFallbackImplementation;

        private Optional<Integer> executionTimeoutMillis = Optional.empty();

        private Retryer retryer = new Retryer.Default(0L, 0L, 1);

        private boolean circuitBreaker = true;

        private boolean decode404 = false;

        Builder(Class<T> clientClass) {
            this.clientClass = clientClass;
        }

        public Builder<T> withBasicAuthentication(String userName, String password) {
            feignBuilder.requestInterceptor(new BasicAuthRequestInterceptor(userName, password));
            return this;
        }

        public Builder<T> withXApiKeyAuthentication(String xApiKey) {
            feignBuilder.requestInterceptor(template -> template.header("X-API-KEY", xApiKey));
            return this;
        }

        public Builder<T> withCustomizedObjectMapper(Consumer<ObjectMapper> customizationFunction) {
            customizationFunction.accept(objectMapper);
            return this;
        }

        public Builder<T> withEncoder(Encoder encoder) {
            feignBuilder.encoder(encoder);
            return this;
        }

        public Builder<T> withDecoder(Decoder decoder) {
            feignBuilder.decoder(decoder);
            return this;
        }

        public Builder<T> withErrorDecoder(ErrorDecoder errorDecoder) {
            feignBuilder.errorDecoder(errorDecoder);
            return this;
        }

        public Builder<T> withFallback(T fallbackImplementation) {
            this.nullableFallbackImplementation = fallbackImplementation;
            return this;
        }

        public Builder<T> withDecode404() {
            this.decode404 = true;
            return this;
        }

        public Builder<T> withExecutionTimeout(int executionTimeout) {
            executionTimeoutMillis = Optional.of(executionTimeout);
            return this;
        }

        public Builder<T> withRetry(long period, long maxPeriod, int maxAttempts) {
            retryer = new Retryer.Default(period, maxPeriod, maxAttempts);
            return this;
        }

        public Builder<T> withCircuitBreaker(boolean circuitBreaker) {
            this.circuitBreaker = circuitBreaker;
            return this;
        }

        private HystrixCommand.Setter createCommandGroupSetter(String commandName) {
            return HystrixCommand.Setter
                    .withGroupKey(HystrixCommandGroupKey.Factory.asKey(commandGroupName))
                    .andCommandKey(HystrixCommandKey.Factory.asKey(commandName))
                    .andCommandPropertiesDefaults(
                            setOptionalExecutionTimeout(HystrixCommandProperties.Setter().withCircuitBreakerEnabled(circuitBreaker)))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withCoreSize(poolSize)
                            .withMaxQueueSize(poolSize * 10)
                            .withQueueSizeRejectionThreshold(poolSize * 10));
        }

        private HystrixCommandProperties.Setter setOptionalExecutionTimeout(HystrixCommandProperties.Setter propsSetter) {
            propsSetter.withExecutionTimeoutEnabled(executionTimeoutMillis.isPresent());
            executionTimeoutMillis.ifPresent(propsSetter::withExecutionTimeoutInMilliseconds);
            return propsSetter;
        }

        public T buildClient() {
            if (decode404) {
                feignBuilder.decode404();
            }

            return feignBuilder
                    .retryer(retryer)
                    .setterFactory((t, method) -> createCommandGroupSetter(method.getName()))
                    .target(clientClass, basePath, nullableFallbackImplementation);
        }

    }

}
