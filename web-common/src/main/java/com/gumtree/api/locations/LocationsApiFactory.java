package com.gumtree.api.locations;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.api.locations.infrastructure.LocationsApi;
import feign.Feign;
import feign.Request;
import feign.Retryer;
import feign.auth.BasicAuthRequestInterceptor;
import feign.jackson.JacksonDecoder;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;

import static com.fasterxml.jackson.databind.PropertyNamingStrategy.SNAKE_CASE;


public final class LocationsApiFactory {
    public static LocationsApi create(String url, int connectionTimeout, int readTimeout, String ingressUser, String ingressPassword,
                                      MeterRegistry registry) {
        ObjectMapper objectMapper = new ObjectMapper()
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.READ_ENUMS_USING_TO_STRING, true)
                .setPropertyNamingStrategy(SNAKE_CASE);

        // doesn't use HystrixFeign because requests are wrapped in commands in services
        return Feign.builder()
                .client(new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                        .addInterceptor(new OkHttpClientMetricsInterceptor(registry))
                        .build()))
                .decode404()
                .decoder(new JacksonDecoder(objectMapper))
                .options(new Request.Options(connectionTimeout, readTimeout))
                .retryer(new Retryer.Default(0L, 0L, 2)) // 1 means no retry, 2 means 1 retry
                .requestInterceptor(new BasicAuthRequestInterceptor(ingressUser, ingressPassword))
                .target(LocationsApi.class, url);
    }

    private LocationsApiFactory() {
    }
}
