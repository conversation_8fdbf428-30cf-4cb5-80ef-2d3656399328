package com.gumtree.config;

import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.util.Optional;

public enum SellerProperty implements GtProps.GtProperty {

    GUMTREE_HOST("gumtree.host", "Gumtree site URL"),
    GUMTREE_HOST_NEW("gumtree.host.newgumtree", "www Gumtree site url"),
    GUMTREE_HOST_NEW_BASE_URI("gumtree.host.new.base_uri", "www Gumtree site url"),
    MY_GUMTREE_HOST("gumtree.host.mygumtree", "My Gumtree site URL"),
    BAPI_HOST("gumtree.bapi.host", ""),
    BAPI_CONNECTION_TIMEOUT("gumtree.bapi.connection.timeout", ""),
    BAPI_SOCKET_TIMEOUT("gumtree.bapi.socket.timeout", ""),
    BAPI_MAX_ATTEMPTS("gumtree.bapi.max_attempts.count",""),
    BAPI_SECRET_HEADER("gumtree.bapi.secret.header", "Bapi secret header"),
    COOKIES_DOMAIN("gumtree.cookies.default_domain", ""),
    IMAGE_DOMAIN("gumtree.image.domain.dev", ""),
    BUYER_BASE_URL("gumtree.url.buyer.base_uri", ""),
    SELLER_BASE_URL("gumtree.url.seller.base_uri", ""),
    SELLER_SECURE_BASE_URL("gumtree.url.seller.secure.base_uri", ""),
    SALESFORCE_CLIENT_ID("gumtree.salesforce.clientId", ""),
    SALESFORCE_CLIENT_SECRET("gumtree.salesforce.clientSecret", ""),
    SALESFORCE_REDIRECT_URI("gumtree.salesforce.redirectUri", ""),
    SALESFORCE_JWT_SECRET("gumtree.salesforce.jwtSecret", ""),
    GUMTREE_REMEMBERME_AES128_BASE64_KEY("gumtree.rememberme.aes128.base64.key", ""),
    GUMTREE_REMEMBERME_AES128_BASE64_PREVIOUS_KEY("gumtree.rememberme.aes128.base64.previous.key", ""),
    GUMTREE_REMEMBERME_AES128_BASE64_NEXT_KEY("gumtree.rememberme.aes128.base64.next.key", ""),
    SALESFORCE_GUMTREE_URL("gumtree.salesforce.url", ""),
    GOOGLE_APP_ID("google.api.clientId", "Google app ID"),
    FACEBOOK_APP_ID("facebook.api.appId", "Facebook app ID"),
    CLIENT_LOG_ENABLED("gumtree.clientlog.enabled", ""),
    CONTACT_EMAIL_LIMIT("gumtree.seller.contact.email.limit", "Contact Email limit"),

    //MESSAGE CENTRE
    MESSAGE_CENTRE_ENABLED("gumtree.messagecentre.enabled", "Message centre feature switch"),
    MESSAGE_CENTRE_LINK_ENABLED("gumtree.messagecentre.link.enabled", "Message centre link feature switch"),
    MESSAGE_POLLING_FREQUENCY("gumtree.messagecentre.pollingfrequency.seconds", "Frequency to use for polling messages in seconds"),
    NOTIFICATION_POLLING_FREQUENCY("gumtree.messagecentre.notification.pollingfrequency.minutes",
                                        "Frequency to use for polling unread messages in minutes"),
    INACTIVE_MESSAGE_POLLING_FREQUENCY("gumtree.messagecentre.inactive.pollingfrequency.seconds",
                                        "Frequency to use for polling messages in ms"),

    // DRAFT ADS API
    DRAFT_ADS_API_HOST("gumtree.drafts.api.host", "The Draft Ads Api host including protocol"),
    DRAFT_ADS_API_PORT("gumtree.drafts.api.port", "The Draft Ads Api port"),
    DRAFT_ADS_API_VERIFY_SSL_CERTS("gumtree.drafts.api.verify_ssl_certs", "The Draft Ads Api, should client verify ssl certificates?"),
    DRAFT_ADS_API_ENABLED("gumtree.drafts.api.enabled", "Is the Draft Ads Api client enabled"),
    DRAFT_ADS_API_CONNECTION_TIMEOUT("gumtree.drafts.api.connection_timeout", "Connection timeout for the Draft Ads Api client"),
    DRAFT_ADS_API_SOCKET_TIMEOUT("gumtree.drafts.api.socket_timeout", "Socket timeout for the Draft Ads Api client"),
    DRAFT_ADS_API_STUB_ENABLED("gumtree.drafts.api.stub.enabled", "Should I start the Draft Ads Api stub?"),

    // RECAPTCHA
    RECAPTCHA_ENABLED("gumtree.recaptcha.enabled","Recaptcha enabled"),
    GUMTREE_IP_LOOKUP_STRATEGY("gumtree.ip.lookup.strategy","Where to find a users IP Address"),

    //ANALYTICS
    GA_TRACKING_ID("gumtree.analytics.ga.trackingid", "Tracking Id for google analytics"),
    GA_HOST_URL("gumtree.analytics.ga.host", "GA host"),

    //MOTORS
    MOTORS_API_HOST("gumtree.motors.api.host", "Motors API host"),
    MOTORS_API_PORT("gumtree.motors.api.port", "Motors API port"),
    MOTORS_API_CONNECTION_TIMEOUT("gumtree.motors.api.connection_timeout", "Motors API connection timeout in millis"),
    MOTORS_API_READ_TIMEOUT("gumtree.motors.api.read_timeout", "Motors API read timeout in millis"),

    //PAYMENT
    PAYMENT_API_HOST("gumtree.payment.api.host", "Payment API host"),
    PAYMENT_API_PORT("gumtree.payment.api.port", "Payment API port"),
    PAYMENT_API_CONNECTION_TIMEOUT("gumtree.payment.api.connection_timeout", "Payment API connection timeout in millis"),
    PAYMENT_API_READ_TIMEOUT("gumtree.payment.api.read_timeout", "Payment API read timeout in millis"),

    // USER REVIEWS
    USER_REVIEWS_API_HOST("gumtree.userreviews.api.host", "Message Box API host"),
    USER_REVIEWS_API_CONNECTION_TIMEOUT("gumtree.userreviews.api.connection_timeout", "Message Box API connection timeout in millis"),
    USER_REVIEWS_API_READ_TIMEOUT("gumtree.userreviews.api.read_timeout", "Message Box API read timeout in millis"),

    // Message Box API
    MESSAGE_BOX_API_HOST("gumtree.messagebox.api.host", "User Reviews API host"),
    MESSAGE_BOX_API_CONNECTION_TIMEOUT("gumtree.messagebox.api.connection_timeout", "User Reviews API connection timeout in millis"),
    MESSAGE_BOX_API_READ_TIMEOUT("gumtree.messagebox.api.read_timeout", "User Reviews API read timeout in millis"),
    MESSAGE_BOX_API_RETRIES("gumtree.messagebox.api.retries", "Message Box API max number of retries of failed request"),

    // CASSANDRA
    CASSANDRA_ENABLED("gumtree.seller.db.cassandra.enabled", "true|false"),
    CASSANDRA_SERVERS("gumtree.seller.db.cassandra.servers", "Configuration format: 'host1[:port1],[hostN[:portN]]'"),
    CASSANDRA_USER_NAME("gumtree.seller.db.cassandra.username", ""),
    CASSANDRA_PASSWORD("gumtree.seller.db.cassandra.password", ""),
    CASSANDRA_KEYSPACE("gumtree.seller.db.cassandra.keyspace", ""),

    // category api
    CATSAPI_HOST("gumtree.category.api.hostname", ""),
    CATSAPI_PORT("gumtree.category.api.port", ""),
    CATSAPI_SOCKET_TIMEOUT("gumtree.category.api.socket.timeout", ""),
    CATSAPI_CONNECTION_TIMEOUT("gumtree.category.api.connection.timeout", ""),
    CATSAPI_RETRY_COUNT("gumtree.category.api.retry.count", ""),
    CATSAPI_CACHE_RELOAD_INTERVAL("gumtree.category.api.cache.reload.interval", ""),

    // Posting
    MAX_IMAGE_UPLOAD_SIZE("gumtree.seller.max.image.upload.size", "Maximum image upload size in bytes"),

    // GTALL-8659: FORCE login when replying to all advert categories except those listed here (2553 (Jobs))
    REPLY_LOGIN_WHITELISTED_CATEGORIES_ENABLED("gumtree.reply.login.whitelisted.categories.enabled",
                                         "if true, allows non-logged users to reply to an advert of certain categories"),
    REPLY_LOGIN_WHITELISTED_CATEGORIES("gumtree.reply.login.whitelisted.categories",
                                 "List of advert categories that DO NOT require login to reply"),

    // Locations
    LOCATIONS_URL("gumtree.locations.url", "Locations URL"),

    // MOTORS PRICE GUIDANCE
    MOTORS_PRICE_GUIDANCE_BASE_URL("gumtree.motors.price.guidance.baseurl", "Base URL"),
    MOTORS_PRICE_GUIDANCE_CONNECTION_TIMEOUT("gumtree.motors.price.guidance.connection_timeout", "Connection Timeout"),
    MOTORS_PRICE_GUIDANCE_READ_TIMEOUT("gumtree.motors.price.guidance.read_timeout", "Read Timeout"),
    PARAMETER_ENCRYPTION_KEY("gumtree.rememberme.aes128.base64.key", ""),

    // Media Processor
    MEDIA_PROCESSOR_HOST("gumtree.media_processor.host","Bapi host"),
    MEDIA_PROCESSOR_CONNECTION_TIMEOUT("gumtree.media_processor.connection.timeout",""),
    MEDIA_PROCESSOR_SOCKET_TIMEOUT("gumtree.media_processor.socket.timeout",""),
    MEDIA_PROCESSOR_MAX_ATTEMPTS("gumtree.media_processor.max_attempts.count",""),

    API_JWT_PRIVATE_KEY("gumtree.api.jwt.private.key", "Used to encrypt data stored in token"),
    API_JWT_VALID_NOT_BEFORE_SECONDS("gumtree.api.jwt.not.valid.before.seconds", "Token Not Valid Before"),
    API_JWT_VALID_NOT_AFTER_SECONDS("gumtree.api.jwt.not.valid.after.seconds", "Expire Token"),

    //PHONE number authenticator config
    PHOME_NUMBER_AUTHENTICATOR_BASE_URL("gumtree.phone.number.authenticator.baseurl","phone Number Authenticator Base Url"),
    PHOME_NUMBER_AUTHENTICATOR_READ_TIMEOUT("gumtree.phone.number.authenticator.read_timeout","phone Number Authenticator read_timeout"),
    PHOME_NUMBER_AUTHENTICATOR_CONNECTION_TIMEOUT("gumtree.phone.number.authenticator.connection_timeout","connection_timeout"),

    // Autobiz
    AUTOBIZ_EXPERIMENT_LOCATION_ID("gumtree.autobiz.experiment.location.id", "The area for which to turn on the Autobiz flow for cars");

    private String propertyName;
    private String description;


    SellerProperty(String propertyName, String description) {
        this.propertyName = propertyName;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return propertyName;
    }

    public String getDescription() {
        return description;
    }

    public static final class RecaptchaEnabled implements PropSupplier<Boolean> {
        private final Optional<Boolean> value;

        public RecaptchaEnabled() {
            this.value = Optional.empty();
        }

        public RecaptchaEnabled(@Nonnull Boolean value) {
            Assert.notNull(value);
            this.value = Optional.of(value);
        }

        @Override
        public Boolean get() {
            if (value.isPresent()) {
                return value.get();
            } else {
                return GtProps.getDBool(SellerProperty.RECAPTCHA_ENABLED).get();
            }
        }
    }
}
