package com.gumtree.web.api;

public class WebApiResponse<T> {
    private final T value;

    public static WebApiResponse<Boolean> ofBoolean(Boolean value) {
        return new WebApiResponse(value);
    }

    public static WebApiResponse<Boolean> ofTrue() {
        return new WebApiResponse(true);
    }

    public static WebApiResponse<Boolean> ofFalse() {
        return new WebApiResponse(false);
    }

    public WebApiResponse(T value) {
        this.value = value;
    }

    public T getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WebApiResponse)) return false;

        WebApiResponse<?> that = (WebApiResponse<?>) o;

        return value != null ? value.equals(that.value) : that.value == null;
    }

    @Override
    public int hashCode() {
        return value != null ? value.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "WebApiResponse{" +
                "value=" + value +
                '}';
    }
}
