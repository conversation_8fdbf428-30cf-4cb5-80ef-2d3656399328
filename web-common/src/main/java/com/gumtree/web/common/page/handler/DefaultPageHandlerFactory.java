package com.gumtree.web.common.page.handler;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;

/**
 * Default implementation of {@link PageHandlerFactory}.
 */
@Component
public final class DefaultPageHandlerFactory implements PageHandlerFactory {

    private Cache<Method, HandlerAnnotations> annotationsCache;

    /**
     * Constructor
     */
    public DefaultPageHandlerFactory() {

        annotationsCache = CacheBuilder.newBuilder().build(
                new CacheLoader<Method, HandlerAnnotations>() {

                    @Override
                    public HandlerAnnotations load(Method key) throws Exception {
                        return new HandlerAnnotations(
                                toList(key.getAnnotations()),
                                toList(key.getDeclaringClass().getAnnotations()));
                    }
                });
    }

    @Override
    public PageHandler create(final HandlerMethod method) {
        try {
            HandlerAnnotations handlerAnnotations = annotationsCache.get(method.getMethod(),
                    new Callable<HandlerAnnotations>() {
                @Override
                public HandlerAnnotations call() throws Exception {
                    return new HandlerAnnotations(
                            toList(method.getMethod().getAnnotations()),
                            toList(method.getMethod().getDeclaringClass().getAnnotations()));
                }
            });
            return new DefaultPageHandler(handlerAnnotations.methodAnnotations, handlerAnnotations.classAnnotations);
        } catch (MissingGumtreePageAnnotationException e) {
            return null;
        } catch (ExecutionException e) {
            return null;
        }
    }

    /**
     * Model for caching method and class annotations.
     */
    private final class HandlerAnnotations {

        private Collection<Annotation> methodAnnotations;

        private Collection<Annotation> classAnnotations;

        /**
         * Constructor.
         *
         * @param methodAnnotations the method annotations
         * @param classAnnotations  the class annotations
         */
        private HandlerAnnotations(
                List<Annotation> methodAnnotations,
                List<Annotation> classAnnotations) {

            this.methodAnnotations = methodAnnotations;
            this.classAnnotations = classAnnotations;
        }
    }

    private List<Annotation> toList(Annotation[] annotations) {
        if (annotations != null) {
            return Arrays.asList(annotations);
        }
        return null;
    }
}
