package com.gumtree.web.common.domain.ad;

import javaslang.control.Try;

import java.util.Optional;

public enum ReplyType {
    GUMTREE,
    TRIFECTA,
    MADGEX;

    public static <T extends Enum<T>> Optional<ReplyType> maybeOf(T value) {
        return Optional.ofNullable(value).map(Enum::name).flatMap(ReplyType::maybeOf);
    }

    public static Optional<ReplyType> maybeOf(String value) {
        return Try.of(() -> ReplyType.valueOf(value)).toJavaOptional();
    }

    public static ReplyType nothing() {
        return null;
    }
}
