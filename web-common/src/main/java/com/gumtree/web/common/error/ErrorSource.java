package com.gumtree.web.common.error;

/**
 * A source of errors.
 */
public interface ErrorSource {

    /**
     * @return if this source contains global errors
     */
    boolean containsGlobalErrors();

    /**
     * Do the errors contain a field error for the specified field?
     *
     * @param field the field to lookup
     * @return true if the source contains the specified error
     */
    boolean containsFieldError(String field);
}
