package com.gumtree.web.common.sapi;

public enum AdvertAttributeNames {
    VEHICLE_MAKE("vehicle_make"),
    VEHICLE_TRANSMISSION("vehicle_transmission"),
    VEHICLE_MODEL("vehicle_model"),
    VEHICLE_MILEAGE("vehicle_mileage"),
    VEHICLE_COLOUR("vehicle_colour"),
    VEHICLE_ENGINE_SIZE("vehicle_engine_size"),
    PRICE_FREQUENCY("price_frequency"),
    SELLER_TYPE("seller_type"),
    VEHICLE_FUEL_TYPE("vehicle_fuel_type"),
    VEHICLE_REGISTRATION_YEAR("vehicle_registration_year"),
    VEHICLE_BODY_TYPE("vehicle_body_type"),
    MOTORBIKE_MAKE("motorbike_make"),
    JOB_CONTRACT_TYPE("job_contract_type"),
    PROPERTY_TYPE("property_type"),
    PROPERTY_COUPLES("property_couples"),
    PROPERTY_ROOM_TYPE("property_room_type"),
    EVENT_DATE("event_date"),
    TRAVEL_DATE("travel_date"),
    LAWN_MOWER_TYPE("lawn_mower_type"),
    PROPERTY_NUMBER_BEDS("property_number_beds"),
    VEHICLE_AGE("vehicle_age"),
    DATE_AVAILABLE("available_date"),
    SELL_TO_CBB("sell_to_cbb"),
    PRICE("price"),
    SALARY_MIN("salary_min"),
    SALARY_MAX("salary_max"),
    VEHICLE_VHC_CHECKED("vehicle_vhc_checked"),
    VRN("vrn");

    private String name;

    private AdvertAttributeNames(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }
}

