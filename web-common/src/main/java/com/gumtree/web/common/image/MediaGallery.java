package com.gumtree.web.common.image;

import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.domain.media.Video;
import com.gumtree.util.url.UrlScheme;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The image url container is used to hold the required urls for an advert or any other
 * class that requires them for page display.  The imageComponentContainer is rendered by the
 * tag mediaGallery.tag rendering the urls
 */

public class MediaGallery {
    private static final Pattern YOUTUBE_ID_PATTERN =
            Pattern.compile("^$|^.*((?:[a-z]+\\.)?youtube\\.com/)(?:watch\\?v=|v/)([\\w\\-]+).*?$");
    private static final String PICTURE = " Picture";
    private String vipUrl;
    private String altText;
    private Collection<Video> videos;
    private List<MediaComponent> mediaComponentList = new ArrayList<MediaComponent>();
    private Boolean hasMedia = false;
    private Boolean singleImage = true;
    private Boolean noImages = true;
    private Boolean hasVideo = false;
    private MediaComponent mainImage;

    /**
     * Construct a mediaGallery
     *
     * @param advert    vip advert
     * @param urlScheme autowired urlScheme
     */
    public MediaGallery(Advert advert, UrlScheme urlScheme) {
        Assert.notNull(advert);
        Assert.notNull(urlScheme);
        this.vipUrl = urlScheme.urlFor(advert);
        if (advert.getLocationText() != null) {
            this.altText = StringUtils.concat(advert.getTitle(), " ", advert.getLocationText() + PICTURE);
        } else {
            this.altText = advert.getTitle() + PICTURE;
        }
        this.videos = advert.getVideos();
        populateMainImage(advert, urlScheme);
        populateImages(advert, urlScheme);
        populateVideos(advert, urlScheme);

        if (!this.hasMedia && this.hasVideo) {
            this.mainImage = mediaComponentList.get(0);
            mediaComponentList.remove(0); //remove video from the list so its not displayed twice
            this.hasMedia = true;
        }
    }

    public final List<MediaComponent> getMediaComponentList() {
        return mediaComponentList;
    }

    public final MediaComponent getMainImage() {
        return mainImage;
    }

    public final String getVipUrl() {
        return vipUrl;
    }

    public final String getAltText() {
        return altText;
    }

    public final Boolean getHasMedia() {
        return hasMedia;
    }

    public final Boolean getHasVideo() {
        return hasVideo;
    }


    public final Boolean getSingleImage() {
        return singleImage;
    }

    public final Boolean getNoImages() {
        return noImages;
    }

    public final Collection<Video> getVideos() {
        return videos;
    }

    private void populateMainImage(Advert advert, UrlScheme urlScheme) {
        Map<ImageSize, String> mainImageUrls = urlScheme.urlsFor(advert.getMainImage());
        if (!mainImageUrls.isEmpty()) {
            mainImage = new MediaComponent(mainImageUrls, MediaType.IMAGE);
            this.hasMedia = true;
        }
    }

    private void populateImages(Advert advert, UrlScheme urlScheme) {
        Iterator<Image> it = advert.getImages().iterator();
        while (it.hasNext() && mediaComponentList.size() < 8) {
            Image image = it.next();
            if (image.getId() != null || image.getBaseUrl() != null) {
                MediaComponent imgUrls = new MediaComponent(urlScheme.urlsFor(image), MediaType.IMAGE);
                mediaComponentList.add(imgUrls);
                this.singleImage = false;
                this.noImages = false;
                this.hasMedia = true;
            }
        }
    }

    private void populateVideos(Advert advert, UrlScheme urlScheme) {
        Iterator<Video> it = advert.getVideos().iterator();
        while (it.hasNext() && mediaComponentList.size() < 8 && !hasVideo) {
            Video video = it.next();
            if (video.getUrl() != null && !"".equals(video.getUrl())) {
                Matcher m = YOUTUBE_ID_PATTERN.matcher(video.getUrl());
                if (m.matches()) {
                    MediaComponent vidUrls =
                            new MediaComponent(createVideoUrlMap(video.getUrl(), urlScheme), MediaType.VIDEO);
                    mediaComponentList.add(vidUrls);
                    this.hasVideo = true;
                    if (hasMedia) {
                        this.singleImage = false;
                        this.noImages = false;
                    }
                }
            }
        }

    }

    private Map<ImageSize, String> createVideoUrlMap(String videoUrl, UrlScheme urlScheme) {
        Map<ImageSize, String> videoUrlMap = new HashMap<ImageSize, String>();
        videoUrlMap.put(ImageSize.THUMB, urlScheme.urlForYoutubeThumbnail(videoUrl));
        videoUrlMap.put(ImageSize.MAIN, urlScheme.urlForYoutubeEmbed(videoUrl));
        videoUrlMap.put(ImageSize.FULL, urlScheme.urlForYoutubeEmbed(videoUrl));
        return videoUrlMap;
    }

}
