package com.gumtree.web.common.device;

import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceResolver;
import org.springframework.mobile.device.DeviceUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class DeviceResolverHandlerInterceptor extends HandlerInterceptorAdapter {

    private final DeviceResolver deviceResolver;

    public DeviceResolverHandlerInterceptor() {
        this.deviceResolver = new DefaultDeviceResolver();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        Device device = deviceResolver.resolveDevice(request);
        request.setAttribute(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE, device);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response,
                           Object handler, ModelAndView modelAndView) {
        if ("XMLHttpRequest".equals(request.getHeader("X-Requested-With"))) {
            return;
        }

        if (modelAndView != null) {
            modelAndView.addObject(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE,
                    request.getAttribute(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE));
        }
    }
}
