package com.gumtree.web.common.util;

import com.gumtree.web.common.ip.RemoteIPArgumentResolver;
import org.apache.http.Header;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;


/**
 * Utility class for methods relating to requests.
 */
public final class RequestUtils {

    private static final String UTM_MEDIUM_PARAM_NAME = "utm_medium";
    private static final String UTM_SOURCE_PARAM_NAME = "utm_source";

    /**
     * Private constructor to prevent misuse.
     */
    private RequestUtils() {
    }

    /**
     * Check if a model/view is a redirect or not.
     *
     * @param modelAndView the model/view to check
     * @return true if the response will be a redirect
     */
    public static boolean isRedirect(ModelAndView modelAndView) {
        if (modelAndView.getViewName() != null) {
            return modelAndView.getViewName().startsWith("redirect:");
        } else if (modelAndView.getView() != null) {
            return modelAndView.getView() instanceof RedirectView;
        }
        return false;
    }

    /**
     * Is an user commit to the site from google by clicking on our pay for click advertising?
     * @param request the request
     * @return <coode>true</coode> if the user visits the site from google by clicking on our advert
     */
    public static boolean isGooglePayForClickVisit(HttpServletRequest request) {
        return request.getParameter(UTM_MEDIUM_PARAM_NAME) != null
                && "cpc".equalsIgnoreCase(request.getParameter(UTM_MEDIUM_PARAM_NAME));
    }

    /**
     * Is an user commit to the site from Criteo by clicking on our pay for click advertising?
     * @param request the request
     * @return <coode>true</coode> if the user visits the site from Criteo by clicking on our advert
     */
    public static boolean isCriteoPayForClickVisit(HttpServletRequest request) {
        return request.getParameter(UTM_SOURCE_PARAM_NAME) != null
                && "Criteo".equalsIgnoreCase(request.getParameter(UTM_SOURCE_PARAM_NAME));
    }

    /**
     * Read the 1st value of the given header from the response.
     * The header can have list of values so this method returns 1st one.
     *
     * @param resp the response to read header from
     * @param headerName the header name
     *
     * @return the header
     */
    public static Optional<Header> get1stHeaderValue(ResponseEntity<String> resp, String headerName) {
        HttpHeaders headers = resp.getHeaders();
        if (headers != null) {
            List<String> values = headers.get(headerName);
            if (values != null && !values.isEmpty()) {
                Optional.ofNullable(values.get(0));
            }
        }

        return Optional.empty();
    }

    public static Optional<String> resolveRemoteIp(HttpServletRequest request) {
        return RemoteIPArgumentResolver.resolve(request).map(remoteIP -> remoteIP.getIpAddress());
    }

}
