package com.gumtree.web.common.page.context;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.legacy.LegacySite;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.zeno.core.domain.PageType;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * Controller handler methods should be injected with an instance of this class to enable them
 * to configure common page behaviours.
 */
public interface GumtreePageContext<T> {

    /**
     * @return if context has been initialised.
     */
    boolean isInitialised();

    /**
     * Get the page type.
     *
     * @return the page type
     */
    PageType getPageType();

    /**
     * Override the page type
     *
     * @param pageType new page type
     */
    void setPageType(PageType pageType);

    /**
     * Get the current user's homepage location.
     *
     * @return the current user's homepage location.
     */
    Location getHomepageLocation();

    /**
     * Get the location.
     *
     * @return the location.
     */
    Location getLocation();

    /**
     * @return the location level hierarchy for the contextual location.
     */
    Map<Integer, Location> getLocationHierarchy();

    /**
     * Get the legacy site.
     *
     * @return the legacy site.
     */
    LegacySite getLegacySite();

    /**
     * @param location - location to set
     */
    void setLocation(Location location);

    /**
     * Get the county for the location.
     *
     * @return the county for the location.
     */
    Location getCounty();

    /**
     * Get the category.
     *
     * @return the category.
     */
    Category getCategory();

    /**
     * Set the category
     *
     * @param category the category
     */
    void setCategory(Category category);

    /**
     * @param levelHierarchy set category level hierarchy
     */
    void setCategoryLevelHierarchy(Map<Integer, Category> levelHierarchy);

    /**
     * Get the L1 category.
     *
     * @return the L1 category.
     */
    Category getL1Category();

    /**
     * @return the category level hierarchy for the contextual category.
     */
    Map<Integer, Category> getCategoryLevelHierarchy();

    /**
     * @return the full request URL for the current request.
     */
    URL getAbsoluteRequestUrl();

    /**
     * @return referrer header for current request.
     */
    String getReferrer();

    /**
     *
     * @return
     */
    HttpServletRequest getHttpServletRequest();


    /**
     * @return {@link ExecutorService} for running up response content generation tasks in parallel.
     */
    ExecutorService getExecutorService();

    /**
     * @return the order
     */
    Order getOrder();

    /**
     * @param order the order
     */
    void setOrder(Order order);

    /**
     * @param account the account
     */
    void setAccount(Account account);

    /**
     * @return the account
     */
    Account getAccount();

    /**
     * @param user the user
     */
    void setUser(User user);

    /**
     * @return the user
     */
    User getUser();

    /**
     * @param searchTerm - any search term the user has entered
     */
    void setSearchTerm(String searchTerm);

    /**
     * @return - search term the user has entered, if any
     */
    String getSearchTerm();

    /**
     * @param hasContent - whether the page displayed any results
     */
    void setHasContent(Boolean hasContent);

    /**
     * @return - true if the page displayed any results
     */
    Boolean hasContent();

    /**
     * @param displayAdsViewMode ads view mode
     */
    void setDisplayAdsViewMode(DisplayAdsViewMode displayAdsViewMode);

    /**
     * @return ads view mode
     */
    DisplayAdsViewMode getDisplayAdsViewMode();

    /**
     * @param currentPageNumber current search page number
     */
    void setPageNumber(Integer currentPageNumber);

    /**
     * @return current search page number
     */
    Integer getPageNumber();

    /**
     * @param url - seo url (if different from  absolute url retrieved from HttpServletRequest)
     */
    void setUrl(String url);

    /**
     * @return - seo url
     */
    String getUrl();

    /**
     * @param abTestTypes ab test types
     */
    void setAbTestTypes(List<AbTestType> abTestTypes);

    /**
     * @return ab test types
     */
    List<AbTestType> getAbTestTypes();

    /**
     * @param model page specific model
     */
    void setPageModel(T model);

    /**
     * @return page specific model
     */
    T getPageModel();

    /**
     * @return the location user input
     */
    String getLocationUserInput();

    /**
     * @return the location user input
     */
    void setLocationUserInput(String userInput);

    /**
     * @return is thi a radial search
     */
    Boolean isRadialSearch();

    /**
     * Is this a radial search?
     * @param isRadialSearch Boolean
     */
    void setRadialSearch(Boolean isRadialSearch);

    /**
     * This gets the device type for tracking
     * @return desktop-devicetype
     *
     */
    String getDeviceType();
}
