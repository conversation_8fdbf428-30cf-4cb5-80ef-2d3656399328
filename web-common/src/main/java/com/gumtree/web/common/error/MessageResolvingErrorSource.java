package com.gumtree.web.common.error;

import java.util.List;
import java.util.Map;

/**
 * An {@link ErrorSource} that is capable of resolving messages (from message codes).
 */
public interface MessageResolvingErrorSource extends ErrorSource {

    /**
     * Get resolved error messages for the given field.
     *
     * @param field the field to fetch resolved messages for
     * @return the resolved messages for the field or null if no error exists for field.
     */
    List<String> getResolvedFieldErrorMessages(String field);

    /**
     * @return resolved error messages for all global error.
     */
    List<String> getResolvedGlobalErrorMessages();

    /**
     * @return all resolved error messages for all field errors.
     */
    Map<String, List<String>> getAllResolvedFieldErrorMessages();
}
