package com.gumtree.web.common.util;

/**
 * Interface for supporting creation of class instances from given {@link Class} types.
 */
public interface InstanceCreator {

    /**
     * Create an instance of the given {@link Class}
     *
     * @param clazz the {@link Class} to create an instance of.
     * @param <T>   the generic type
     * @return an instance of the given {@link Class}
     */
    <T> T createInstance(Class<T> clazz);
}
