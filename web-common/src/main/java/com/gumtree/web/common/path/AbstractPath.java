package com.gumtree.web.common.path;

import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

import static com.gumtree.web.common.path.PathBuilder.path;

public abstract class AbstractPath implements Path {

    private Map<String, String> params = new TreeMap<>();

    @Override
    public Path addQueryParam(String key, String value) {
        params.put(key, value);
        return this;
    }

    @Override
    public Path addQueryParam(String key, int value) {
        params.put(key, Integer.toString(value));
        return this;
    }

    @Override
    public Path addQueryParam(String key, Double value) {
        params.put(key, Double.toString(value));
        return this;
    }

    @Override
    public String getPath() {
        PathBuilder builder = path().addPathSegment(getPathSegment());
        addQueryString(builder);
        return builder.build();
    }

    protected void addQueryString(PathBuilder builder) {
        if (!params.isEmpty()) {
            for (String key : params.keySet()) {
                builder.addQueryParam(key, params.get(key));
            }
        }
    }

    protected boolean hasQueryParams() {
        return params != null && !params.isEmpty();
    }

    protected Map<String, String> getParameters() {
        return Collections.unmodifiableMap(params);
    }

    protected abstract String getPathSegment();

    /**
     * Check is parameter already exists.
     *
     * @param key key to check
     * @return true if query param exists, false otherwise
     */
    public boolean containsQueryParam(String key) {
        return params.containsKey(key);
    }

    @Override
    public void removeQueryParam(String key) {
        if (!params.isEmpty() && params.containsKey(key)) {
            params.remove(key);
        }

    }
}
