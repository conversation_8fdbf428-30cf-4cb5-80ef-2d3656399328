package com.gumtree.seo;

import com.google.common.base.Optional;
import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Implementation of the {@link CategorySeoConfig} that loads category settings from YAML file
 */
public class YamlCategorySeoConfigs implements CategorySeoConfig {
    private Map<String, Map<Set<String>, Map<String, String>>> categories;

    public Map<String, Map<Set<String>, Map<String, String>>> getCategories() {
        return categories;
    }

    public void setCategories(Map<String, Map<Set<String>, Map<String, String>>> categories) {
        this.categories = categories;
    }

    /**
     * {@inheritDoc}
     */
    public Map<String, String> getPageConfig(List<String> categoryPath, Set<String> pageId) {
        Map<String, String> result = new HashMap<String, String>();
        if (categoryPath != null && !categoryPath.isEmpty()) {
            int levels = categoryPath.size();
            int currentLevel = 1;
            for (String category: categoryPath) {
                Optional<Map<String, String>> pageConfig = getPageConfig(category, pageId);
                if (pageConfig.isPresent()) {
                    result.putAll(pageConfig.get());
                }

                if (currentLevel == levels) { // is leaf hierarchy
                    pageConfig = getPageConfig(category, getPageIdWithoutCategory(pageId));
                    if (pageConfig.isPresent()) {
                        result.putAll(pageConfig.get());
                    }
                }

                currentLevel++;
            }
        }

        return result;
    }

    private Optional<Map<String, String>> getPageConfig(String name, Set<String> pageId) {
        Map<Set<String>, Map<String, String>> categoryConfig = categories.get(name);
        if (categoryConfig != null) {
            Map<String, String> pageConfig = categoryConfig.get(pageId);
            if (pageConfig != null) {
                return Optional.of(pageConfig);
            }
        }

        return Optional.absent();
    }

    private Set<String> getPageIdWithoutCategory(Set<String> pageId) {
        if (pageId != null && pageId.contains(SEOPageProperty.CATEGORY.getKey())) {
            return Sets.difference(pageId, Sets.newHashSet(SEOPageProperty.CATEGORY.getKey()));
        }

        return pageId;
    }
}
