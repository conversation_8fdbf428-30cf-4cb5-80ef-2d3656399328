<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gumtree.maven-parent</groupId>
        <artifactId>core</artifactId>
        <version>5.54.0e942a7</version>
        <relativePath/>
    </parent>

    <name>Gumtree Seller</name>
    <groupId>com.gumtree.web</groupId>
    <artifactId>seller</artifactId>
    <version>3.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>common</module>
        <module>server</module>
        <module>seller-cassandra-db</module>
        <module>web-common</module>
        <module>google-analytics</module>
        <module>reporting</module>
        <module>content</module>
        <module>cookie</module>
        <module>security</module>
        <module>test-utils</module>
        <module>legacy</module>
    </modules>

    <properties>
        <!-- Pipeline dependencies versions -->
        <gt.version>${project.version}</gt.version>
        <gt.gumtree.common.version>4.58155</gt.gumtree.common.version>
        <gt.category.api.client.version>7.24.525df7e</gt.category.api.client.version>
        <gt.stats.version>4.58222</gt.stats.version>
        <gt.bapi.client.version>7.53.bff2a13</gt.bapi.client.version>
        <gt.gumtree.metrics.version>5.11.92f4f91</gt.gumtree.metrics.version>
        <gt.gumshield.api.client.version>4.58156</gt.gumshield.api.client.version>
        <gt.zeno.version>5.83</gt.zeno.version>
        <gt.threatmetrix.contract.version>2023-04-04-13-18-47.master.261604e</gt.threatmetrix.contract.version>
        <gt.user.service.version>4.58163</gt.user.service.version>
        <gt.shared.jetty.commons.version>5.5.79d4f47</gt.shared.jetty.commons.version>
        <gt.shared.jobs.utils.version>5.0</gt.shared.jobs.utils.version>
        <gt.gumtree.shared.common.properties.version>5.9</gt.gumtree.shared.common.properties.version>
        <gt.draft.ads.api.client.version>4.276</gt.draft.ads.api.client.version>
        <gt.motors.vehicle.data.service.version>5.129</gt.motors.vehicle.data.service.version>
        <gt.user-reviews-api-contracts.version>1.20180904.10</gt.user-reviews-api-contracts.version>
        <gt.cvstore.client.version>5.25</gt.cvstore.client.version>
        <gt.madgex.gumtreeapi.client.version>1.14.4aa2b61-master</gt.madgex.gumtreeapi.client.version>
        <gt.job-applications-contracts.version>1.39.fa4167b-master</gt.job-applications-contracts.version>
        <gt.shiro.extensions.version>1.14.1be0270</gt.shiro.extensions.version>
        <gt.shared-feign-hystrix-api-client.version>5.6.1091422</gt.shared-feign-hystrix-api-client.version>
        <ecg.encryption.tools.version>1.2</ecg.encryption.tools.version>
        <freespee.client.version>1.52</freespee.client.version>

        <!-- libraries -->
        <java.version>1.8</java.version>
        <spring.version>4.0.6.RELEASE</spring.version>
        <spring.mobile.version>1.1.0.RELEASE</spring.mobile.version>
        <aspectj.version>1.7.4</aspectj.version>
        <logback.version>1.1.6</logback.version>
        <scala.version>2.11.8</scala.version>
        <shiro.version>1.2.2</shiro.version>
        <ehcache.version>2.6.7</ehcache.version>
        <httpclient.version>4.3.4</httpclient.version>
        <httpcore.version>4.3.3</httpcore.version>
        <jetty8.version>8.1.15.v20140411</jetty8.version>
        <jetty9.version>9.0.2.v20130417</jetty9.version>
        <fasterxml.jackson.version>2.7.3</fasterxml.jackson.version>
        <gas.version>0.1.27</gas.version>
        <tomcat.version>7.0.54</tomcat.version>
        <slf4j.version>1.7.15</slf4j.version>
        <guava.version>19.0</guava.version>
        <rewrite.version>2.0.12.Final</rewrite.version>
        <tika.version>1.14</tika.version>
        <resteasy.version>2.3.5.Final</resteasy.version>
        <jaxb-impl.version>2.3.2</jaxb-impl.version>
        <jackson.version>1.9.13</jackson.version>
        <jackson2.version>2.7.3</jackson2.version>
        <spring.security.version>3.2.4.RELEASE</spring.security.version>
        <akka.version>2.1.4</akka.version>
        <play-json_2.11.version>2.3.10</play-json_2.11.version>
        <javassist.version>3.20.0-GA</javassist.version>
        <swagger-annotations.version>1.5.12</swagger-annotations.version>
        <!-- tests -->
        <junit.version>4.13</junit.version>
        <hamcrest.version>1.3</hamcrest.version>
        <mockito.version>1.10.19</mockito.version>
        <cucumber.jvm.version>5.2.0</cucumber.jvm.version>
        <camel-core.version>2.16.0</camel-core.version>
        <camel-test.version>2.16.0</camel-test.version>
        <el-impl.version>2.2</el-impl.version>
        <powermock.version>1.6.5</powermock.version>
        <feign.version>9.4.0</feign.version>
        <feign-form.version>3.4.1</feign-form.version>
        <okio.version>2.1.0</okio.version>

        <junit5.version>5.5.2</junit5.version>

        <!-- maven plugins config -->
        <skipTests>false</skipTests>
        <!-- if you wish to turn down the output logging from the tests
             globally this can be set on the command line
             -Dlogging.root.level=WARN -->
        <logging.root.level>INFO</logging.root.level>
        <prometheus.version>0.16.0</prometheus.version>
        <jsr305.version>3.0.1</jsr305.version>
        <sonar.java.checkstyle.reportPaths>target/checkstyle-result.xml</sonar.java.checkstyle.reportPaths>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.junit.jupiter</groupId>
                            <artifactId>junit-jupiter-engine</artifactId>
                            <version>${junit5.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <argLine>${argLine} -Xms512m -Xmx1536m -Dlogging.root.level=${logging.root.level}</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>net.alchim31.maven</groupId>
                    <artifactId>scala-maven-plugin</artifactId>
                    <version>3.1.6</version>
                    <configuration>
                        <sourceDir>src/main/java</sourceDir>
                        <jvmArgs>
                            <jvmArg>-Xms64m</jvmArg>
                            <jvmArg>-Xmx1024m</jvmArg>
                        </jvmArgs>
                        <scalaVersion>${scala.version}</scalaVersion>
                    </configuration>
                    <executions>
                        <execution>
                            <id>scala-compile-first</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>add-source</goal>
                                <goal>compile</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>scala-test-compile</id>
                            <phase>process-test-resources</phase>
                            <goals>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.scalatest</groupId>
                    <artifactId>scalatest-maven-plugin</artifactId>
                    <version>1.0</version>
                    <configuration combine.self="override">
                        <argLine>-Dlogging.root.level=${logging.root.level} -Xms2048M -Xmx2048M -XX:+CMSClassUnloadingEnabled</argLine>
                        <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                        <junitxml>.</junitxml>
                        <filereports>WDF ScalatestReport.txt</filereports>
                        <testFailureIgnore>false</testFailureIgnore>
                    </configuration>
                    <executions>
                        <execution>
                            <id>test</id>
                            <phase>integration-test</phase>
                            <goals>
                                <goal>test</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>selenium-disabled</id>
            <activation>
                <property>
                    <name>gumtree.test.selenium</name>
                    <value>!true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <configuration>
                            <excludes>
                                <exclude>**/*SeleniumIT.java</exclude>
                            </excludes>
                            <argLine>-Xmx1024m -Dlogging.root.level=${logging.root.level}"</argLine>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <scm>
        <connection>scm:git:ssh://****************************/gumtree/gumtree.git</connection>
        <url>scm:git:ssh://****************************/gumtree/gumtree.git</url>
    </scm>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>cookie</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>test-utils</artifactId>
                <version>${project.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>1.4.4</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.web</groupId>
                <artifactId>el-impl</artifactId>
                <version>${el-impl.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>legacy</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- GUMTREE dependencies -->
            <dependency>
                <groupId>com.gumtree.drafts.api</groupId>
                <artifactId>client</artifactId>
                <version>${gt.draft.ads.api.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.cvstore</groupId>
                <artifactId>client</artifactId>
                <version>${gt.cvstore.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.api.category</groupId>
                <artifactId>read-client</artifactId>
                <version>${gt.category.api.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.api.category</groupId>
                <artifactId>read-api</artifactId>
                <version>${gt.category.api.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.shiro</groupId>
                <artifactId>shiro-extensions</artifactId>
                <version>${gt.shiro.extensions.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.api.contracts</groupId>
                <artifactId>job-applications-contracts-client-java</artifactId>
                <version>${gt.job-applications-contracts.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gumtree.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${gt.gumtree.metrics.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>gumtree-common</groupId>
                        <artifactId>properties</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.mail</groupId>
                        <artifactId>mail</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>gumtree-common</groupId>
                <artifactId>test-utils</artifactId>
                <scope>test</scope>
                <version>${gt.gumtree.common.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>seller-public-static-assets</artifactId>
                <type>war</type>
                <scope>runtime</scope>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>mobile-web-static-assets</artifactId>
                <type>war</type>
                <scope>runtime</scope>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>gumtree-common</groupId>
                <artifactId>reports</artifactId>
                <version>${gt.gumtree.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gumtree.shared-commons</groupId>
                <artifactId>shared-jobs-utils</artifactId>
                <version>${gt.shared.jobs.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.shared-commons.properties</groupId>
                <artifactId>gtprops-spring</artifactId>
                <version>${gt.gumtree.shared.common.properties.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-core-asl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>gumtree-common</groupId>
                <artifactId>util</artifactId>
                <version>${gt.gumtree.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>gumtree-common</groupId>
                <artifactId>legacy</artifactId>
                <version>${gt.gumtree.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>gumtree-common</groupId>
                <artifactId>monitoring</artifactId>
                <version>${gt.gumtree.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>web-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>google-analytics</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>reporting</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>content</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>replyts</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.gumshield.api</groupId>
                <artifactId>client</artifactId>
                <version>${gt.gumshield.api.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.japi</groupId>
                <artifactId>client</artifactId>
                <version>${gt.bapi.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>gumtree-common</groupId>
                        <artifactId>properties</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gumtree.japi</groupId>
                <artifactId>model</artifactId>
                <version>${gt.bapi.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.gumtree.api.category</groupId>
                        <artifactId>read-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-digester</groupId>
                        <artifactId>commons-digester</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gumtree.zeno</groupId>
                <artifactId>core</artifactId>
                <version>${gt.zeno.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.zeno</groupId>
                <artifactId>domain</artifactId>
                <version>${gt.zeno.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.zeno</groupId>
                <artifactId>test-utils</artifactId>
                <version>${gt.zeno.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree</groupId>
                <artifactId>gas_2.10</artifactId>
                <version>${gas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.user</groupId>
                <artifactId>user-client</artifactId>
                <version>${gt.user.service.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>gumtree-common</groupId>
                        <artifactId>properties</artifactId>
                    </exclusion>
                </exclusions>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.gumtree.api.contracts</groupId>
                <artifactId>madgex-gumtreeapi-client</artifactId>
                <version>${gt.madgex.gumtreeapi.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ecg.analytics</groupId>
                <artifactId>encryption-tools</artifactId>
                <version>${ecg.encryption.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.freespee</groupId>
                <artifactId>freespee-client</artifactId>
                <version>${freespee.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.codahale.metrics</groupId>
                        <artifactId>metrics-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.codahale.metrics</groupId>
                        <artifactId>metrics-httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.codahale.metrics</groupId>
                        <artifactId>metrics-graphite</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.gumtree.labs</groupId>
                <artifactId>client</artifactId>
                <version>1.11.3273fa6-master</version>
            </dependency>


            <!-- Jetty dependencies -->
            <dependency>
                <groupId>com.gumtree.shared-commons.jetty</groupId>
                <artifactId>jetty-server-legacy</artifactId>
                <version>${gt.shared.jetty.commons.version}</version>
            </dependency>

            <!-- MAVEN-PARENT dependencies -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-oxm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.mobile</groupId>
                <artifactId>spring-mobile-device</artifactId>
                <version>${spring.mobile.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>1.6.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>spring-cloud-gcp-logging</artifactId>
                <version>3.6.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.checkerframework</groupId>
                        <artifactId>checker-qual</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.errorprone</groupId>
                        <artifactId>error_prone_annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.auto.value</groupId>
                        <artifactId>auto-value-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.annotation</groupId>
                        <artifactId>javax.annotation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.auth</groupId>
                        <artifactId>google-auth-library-oauth2-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.j2objc</groupId>
                        <artifactId>j2objc-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.http-client</groupId>
                        <artifactId>google-http-client-gson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.re2j</groupId>
                        <artifactId>re2j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.auth</groupId>
                        <artifactId>google-auth-library-credentials</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.http-client</groupId>
                        <artifactId>google-http-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.typesafe.play</groupId>
                <artifactId>play-json_2.11</artifactId>
                <version>${play-json_2.11.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.joda</groupId>
                        <artifactId>joda-convert</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.2.2</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>1.1.0.Final</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>1.9.13</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>1.9.13</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.7</version>
            </dependency>
            <dependency>
                <groupId>org.owasp.encoder</groupId>
                <artifactId>encoder</artifactId>
                <version>1.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.13</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>2.2.2</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>1.7.28</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>net.kencochrane.raven</groupId>
                <artifactId>raven-logback</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-reflect</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang.modules</groupId>
                <artifactId>scala-parser-combinators_2.11</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang.modules</groupId>
                <artifactId>scala-xml_2.11</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet.jsp</groupId>
                <artifactId>jsp-api</artifactId>
                <version>2.2</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>2.2.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-web</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-ehcache</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache-core</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.ehcache-spring-annotations</groupId>
                <artifactId>ehcache-spring-annotations</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ebay.ecg</groupId>
                <artifactId>eps-client</artifactId>
                <version>1.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.scannotation</groupId>
                        <artifactId>scannotation</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tiles</groupId>
                <artifactId>tiles-extras</artifactId>
                <version>2.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.velocity</groupId>
                        <artifactId>velocity</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-beanutils</groupId>
                        <artifactId>commons-beanutils</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tiles</groupId>
                <artifactId>tiles-jsp</artifactId>
                <version>2.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.jcsv</groupId>
                <artifactId>jcsv</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.tuckey</groupId>
                <artifactId>urlrewritefilter</artifactId>
                <version>4.0.4</version>
            </dependency>
            <dependency>
                <groupId>ur6lad</groupId>
                <artifactId>markdown-taglib</artifactId>
                <version>0.2</version>
            </dependency>
            <dependency>
                <groupId>org.pegdown</groupId>
                <artifactId>pegdown</artifactId>
                <version>1.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.rjeschke</groupId>
                <artifactId>txtmark</artifactId>
                <version>0.13</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius-core</artifactId>
                <version>0.6.0</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>4.3.1.Final</version>
            </dependency>
            <dependency>
                <groupId>bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.7.3</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.rome</groupId>
                <artifactId>rome</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>1.8.5</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>2.7.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-catalina</artifactId>
                <version>${tomcat.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-tribes</artifactId>
                <version>${tomcat.version}</version>
                <!--<scope>provided</scope>-->
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.10</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.json-simple</groupId>
                <artifactId>json-simple</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20160810</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.23</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>5.4</version>
            </dependency>
            <dependency>
                <groupId>net.sf.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>2.0.19</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.0-api</artifactId>
                <version>1.0.1.Final</version>
            </dependency>

            <!-- hystrix -->
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>1.5.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.code.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-metrics-event-stream</artifactId>
                <version>1.5.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.code.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.gumtree</groupId>
                <artifactId>shared-feign-hystrix-api-client</artifactId>
                <version>${gt.shared-feign-hystrix-api-client.version}</version>
            </dependency>

            <!-- swagger for api contracts -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <!-- openfeign -->
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-core</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-jackson</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-hystrix</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-slf4j</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign.form</groupId>
                <artifactId>feign-form</artifactId>
                <version>${feign-form.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign.form</groupId>
                <artifactId>feign-form-spring</artifactId>
                <version>${feign-form.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign-core</artifactId>
                <version>2.1.1.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>${project.groupId}</groupId>
                        <artifactId>feign-form-spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${jsr305.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>annotations</artifactId>
                <version>${jsr305.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <dependency>
                <groupId>org.openapitools</groupId>
                <artifactId>jackson-databind-nullable</artifactId>
                <version>0.2.1</version>
            </dependency>

            <dependency>
                <groupId>org.ocpsoft.rewrite</groupId>
                <artifactId>rewrite-servlet</artifactId>
                <version>${rewrite.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ocpsoft.rewrite</groupId>
                <artifactId>rewrite-integration-spring</artifactId>
                <version>${rewrite.version}</version>
            </dependency>

            <!-- influx -->
            <dependency>
                <groupId>com.github.davidb</groupId>
                <artifactId>metrics-influxdb</artifactId>
                <version>0.9.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Apache Tika -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers</artifactId>
                <version>${tika.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.cxf</groupId>
                        <artifactId>cxf-rt-rs-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.healthmarketscience.jackcess</groupId>
                        <artifactId>jackcess</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-java7</artifactId>
                <version>${tika.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tika</groupId>
                        <artifactId>tika-parsers</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.healthmarketscience.jackcess</groupId>
                <artifactId>jackcess</artifactId>
                <version>2.1.3</version>
            </dependency>

            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>2.0.0</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-library</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib-nodep</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>xml-apis</groupId>
                <artifactId>xml-apis</artifactId>
                <version>1.4.01</version>
            </dependency>
            <dependency>
                <groupId>spy</groupId>
                <artifactId>spymemcached</artifactId>
                <version>2.8.1</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.1</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jackson-provider</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-xc</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-multipart-provider</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb-impl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>3.1.0.GA</version>
            </dependency>
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>1.9</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.xml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-joda</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_2.10</artifactId>
                <version>${akka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>1.6.1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-joda</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.nekohtml</groupId>
                <artifactId>nekohtml</artifactId>
                <version>1.9.19</version>
            </dependency>

            <!-- User Reviews -->
            <dependency>
                <groupId>com.gumtree.api.contracts</groupId>
                <artifactId>user-reviews-api-contracts</artifactId>
                <version>${gt.user-reviews-api-contracts.version}</version>
                <type>contract</type>
            </dependency>

            <dependency>
                <groupId>com.gumtree.api.contracts</groupId>
                <artifactId>user-reviews-api-client</artifactId>
                <version>${gt.user-reviews-api-contracts.version}</version>
            </dependency>

            <!--locations - used for suggestions, get by id/idName, primary postcode resolution-->
            <dependency>
                <groupId>com.gumtree.api.contracts</groupId>
                <artifactId>locations-contract</artifactId>
                <version>1.20200318.12</version>
                <type>contract</type>
            </dependency>

            <!-- PROMETHEUS dependencies -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.12.5</version>
            </dependency>

            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_common</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_dropwizard</artifactId>
                <version>${prometheus.version}</version>
            </dependency>

            <!-- TEST dependencies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easytesting</groupId>
                <artifactId>fest-assert-core</artifactId>
                <version>2.0M10</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest_2.11</artifactId>
                <version>2.2.6</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.objenesis</groupId>
                        <artifactId>objenesis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>junit</artifactId>
                        <groupId>junit</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mockito-core</artifactId>
                        <groupId>org.mockito</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>4.0.6.RELEASE</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.cucumber</groupId>
                <artifactId>cucumber-java</artifactId>
                <version>${cucumber.jvm.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.cucumber</groupId>
                <artifactId>cucumber-core</artifactId>
                <version>${cucumber.jvm.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.cucumber</groupId>
                <artifactId>cucumber-junit</artifactId>
                <version>${cucumber.jvm.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.cucumber</groupId>
                <artifactId>cucumber-spring</artifactId>
                <version>${cucumber.jvm.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.tlrx</groupId>
                <artifactId>elasticsearch-test</artifactId>
                <version>0.0.9</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>0.8.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.databene</groupId>
                <artifactId>contiperf</artifactId>
                <version>2.1.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava-testlib</artifactId>
                <version>${guava.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>1.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.dbunit</groupId>
                <artifactId>dbunit</artifactId>
                <version>2.4.9</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-core</artifactId>
                <version>${camel-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-rabbitmq</artifactId>
                <version>${camel-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-spring-javaconfig</artifactId>
                <version>${camel-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-metrics</artifactId>
                <version>${camel-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-test</artifactId>
                <version>${camel-test.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-test-spring40</artifactId>
                <version>${camel-test.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ning</groupId>
                <artifactId>async-http-client</artifactId>
                <version>1.8.17</version>
            </dependency>
            <dependency>
                <groupId>com.gumtree.motors</groupId>
                <artifactId>test-utils</artifactId>
                <version>${gt.motors.vehicle.data.service.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.tomakehurst</groupId>
                <artifactId>wiremock-standalone</artifactId>
                <version>2.26.0</version>
            </dependency>
            <dependency>
                <groupId>org.hdrhistogram</groupId>
                <artifactId>HdrHistogram</artifactId>
                <version>2.1.11</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jvm</artifactId>
                <version>3.2.2</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.55</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- TEST dependencies -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>
</project>
