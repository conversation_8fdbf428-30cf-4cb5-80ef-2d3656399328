package com.gumtree.web.reporting.google.ga.impl;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;

/**
 * A default {@link com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer}.
 * Note that the category and location are not configured because most seller pages do not want them set.
 * If the status quo changes, and there are required by the majority of pages, then this default class
 * should probably be updated and any classes that explicitly do NOT want them should be updated to use their own
 * custom configurer - see {@link com.gumtree.web.seller.page.reply.reporting.ReplyGoogleAnalyticsConfigurer}.
 */
public class DefaultGoogleAnalyticsConfigurer implements GoogleAnalyticsConfigurer<Void> {

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<Void> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .addTrackEvent(new PostAdBegin(ctx));
    }

}
