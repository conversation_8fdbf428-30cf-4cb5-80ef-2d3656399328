package com.gumtree.web.reporting.google.ga.events.impl;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class PostAdBegin extends GoogleAnalyticsElementTrackEvent {

    public PostAdBegin(ThirdPartyRequestContext<?> ctx) {
        super(ctx.getPageType(), GoogleAnalyticsTrackEventAction.POST_AD_BEGIN);
        setBindSelector("[ga-event=\\'post-ad-begin\\']");
        setBindEvent("click");
        setLabel(label(ctx));
    }

    private String label(ThirdPartyRequestContext<?> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .build();
    }

}
