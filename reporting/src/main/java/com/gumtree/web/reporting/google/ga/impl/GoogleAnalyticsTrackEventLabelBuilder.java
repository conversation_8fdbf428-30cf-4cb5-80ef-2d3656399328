package com.gumtree.web.reporting.google.ga.impl;

import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;

import javax.annotation.Nullable;
import java.util.Map;

public class GoogleAnalyticsTrackEventLabelBuilder {

    private Map<String, String> builder = Maps.newLinkedHashMap();

    public GoogleAnalyticsTrackEventLabelBuilder adId(Long adId) {
        builder.put("adID", String.valueOf(adId));
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder featured(Boolean featured) {
        builder.put("featured", booleanValue(featured));
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder paid(Boolean paid) {
        builder.put("paid", booleanValue(paid));
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder catId(Category category) {
        builder.put("catID", String.valueOf(category.getId()));
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder lCat(Integer level, Category category) {
        if (category != null) {
            builder.put(String.format("l%dCat", level), String.valueOf(category.getId()));
        }
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder locId(Location location) {
        builder.put("locID", String.valueOf(location.getId()));
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder lLoc(Integer level, Location location) {
        if (location != null) {
            builder.put(String.format("l%dLoc", level), String.valueOf(location.getId()));
        }
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder url(String url) {
        builder.put("url", url);
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder searchTerm(String searchTerm) {
        builder.put("searchTerm", searchTerm);
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder distance(String distance) {
        builder.put("distance", distance);
        return this;
    }

    public GoogleAnalyticsTrackEventLabelBuilder searchLocation(String searchLocation) {
        builder.put("searchLocation", searchLocation);
        return this;
    }

    private String booleanValue(Boolean featured) {
        return featured ? "yes" : "no";
    }

    public String build() {
        return Joiner.on(";").join(Iterables.transform(builder.entrySet(), keyValuePairs()));
    }

    private Function<Map.Entry<String, String>, String> keyValuePairs() {
        return new Function<Map.Entry<String, String>, String>() {
            @Override
            public String apply(@Nullable Map.Entry<String, String> input) {
                return String.format("%s=%s", input.getKey(), input.getValue());
            }
        };
    }

}
