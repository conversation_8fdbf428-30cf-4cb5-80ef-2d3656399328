package com.gumtree.web.reporting.google.ga;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;

/**
 * A builder for configuring Google Analytics report options. This is passed to
 * {@link com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer}s to allow them to configure Google Analytics
 * reports.
 */
public interface GoogleAnalyticsEventsBuilder {

    /**
     * Set the page type.
     *
     * @param pageType the page type
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder pageType(String pageType);

    /**
     * Set the county.
     *
     * @param county the county
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder county(Location county);

    /**
     * Set the category.
     *
     * @param category the category
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder category(Category category);

    /**
     * Set the L1 Category
     *
     * @param l1Category the L1 Category
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder l1Category(Category l1Category);

    /**
     * Set the abTestGroup
     * @param context - third party context
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder abTestGroup(ThirdPartyRequestContext context);

    /**
     * Adds experiment
     * @param customVar - customVar
     * @return this builder
     */
    GoogleAnalyticsEventsBuilder addCustomVar(GoogleAnalyticsCustomVar customVar);
}
