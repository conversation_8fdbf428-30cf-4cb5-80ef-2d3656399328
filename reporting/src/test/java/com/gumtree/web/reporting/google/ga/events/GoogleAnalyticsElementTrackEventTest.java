package com.gumtree.web.reporting.google.ga.events;

import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class GoogleAnalyticsElementTrackEventTest {

    private GoogleAnalyticsElementTrackEvent event;

    @Before
    public void setUp() {
        event = new GoogleAnalyticsElementTrackEvent(PageType.VIP,
                GoogleAnalyticsTrackEventAction.POST_AD_PREVIEW);
    }

    @Test
    public void constructor() {
        assertThat(event.getAction(), equalTo("PostAdPreview"));
        assertThat(event.getCategory(), equalTo("VIP"));
    }

    @Test
    public void getLabel() {
        event.setLabel("label");
        assertThat(event.getLabel(), equalTo("label"));
    }

    @Test
    public void getValue() {
        event.setValue(23);
        assertThat(event.getValue(), equalTo(23));
    }

    @Test
    public void getNoninteraction() {
        event.setNoninteraction(true);
        assertThat(event.getNoninteraction(), equalTo(true));
    }

    @Test
    public void getBindSelector() {
        event.setBindSelector("bindSelector");
        assertThat(event.getBindSelector(), equalTo("bindSelector"));
    }

    @Test
    public void getBindEvent() {
        event.setBindEvent("bindEvent");
        assertThat(event.getBindEvent(), equalTo("bindEvent"));
    }
}
