package org.apache.shiro.web.mgt;

import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.security.shiro.GumtreeAuthenticationToken;
import com.gumtree.web.security.shiro.RememberMeJsonSerializer;
import com.gumtree.web.security.shiro.UserAuthenticationInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.crypto.CipherService;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.subject.WebSubjectContext;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.OffsetDateTime;

import static org.apache.shiro.web.mgt.RemainingMaxAgeRememberMeManager.calculateRemainingMaxAge;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RemainingMaxAgeRememberMeManagerTest {
    private RemainingMaxAgeRememberMeManager rmm;

    private ArgumentCaptor<String> httpHeader;

    @Mock
    protected HttpServletRequest request;

    @Mock
    protected HttpServletResponse response;

    @Mock
    protected WebDelegatingSubject subject;

    @Mock
    protected WebSubjectContext subjectContext;

    private static final byte[] ENC_KEY = Base64.decodeBase64("pbfO1tAjR1zDyxG2cKDz9g==");

    @Before
    public void setup() {
        httpHeader = initMocksForSerialization();

        rmm = Mockito.spy(new RemainingMaxAgeRememberMeManager(ENC_KEY, cipherService(), rememberMeCookieTemplate(), new RememberMeJsonSerializer()));
    }

    @Test
    public void onSuccessfulLoginShouldForgetPreviousIdentity() {
        // when
        rmm.onSuccessfulLogin(subject, createAuthenticationInfo(), OffsetDateTime.now().minusSeconds(10));

        // then
        verify(rmm).forgetIdentity(subject);
    }

    @Test
    public void onSuccessfulLoginShouldSetNewCookie() {
        // when
        rmm.onSuccessfulLogin(subject, createAuthenticationInfo(), OffsetDateTime.now().minusSeconds(10));

        // then
        verify(response, times(2)).addHeader(eq("Set-Cookie"), httpHeader.capture());

        String headerValue = httpHeader.getValue();
        String rememberMeValue = headerValue.substring("rememberMe=".length(), headerValue.indexOf(";"));

        // and
        assertThat(headerValue).contains("; Max-Age=90").as("100 - (now() - created time)");
        assertThat(headerValue).contains("; Domain=dev.gumtree.com");
        assertThat(headerValue).contains("; Path=/;");
        assertThat(headerValue).contains("; Secure; HttpOnly");

        SimplePrincipalCollection principal = (SimplePrincipalCollection) new RememberMeJsonSerializer().deserialize(decrypt(rememberMeValue.getBytes()));
        assertThat(principal.asList().get(0)).isEqualTo("<EMAIL>");
        assertThat(principal.asList().get(1)).isEqualTo(GumtreeAccessToken.createFromString("token"));
    }

    @Test
    public void calculateRemainingMaxAgeTest() {
        assertThat(calculateRemainingMaxAge(OffsetDateTime.now().plusMinutes(10), 100)).isEqualTo(0).as("created time is in the future");
        assertThat(calculateRemainingMaxAge(OffsetDateTime.now().minusSeconds(25), 100)).isEqualTo(75).as("25 seconds passed, 75 remaining");
        assertThat(calculateRemainingMaxAge(OffsetDateTime.now().minusSeconds(100), 100)).isEqualTo(0).as("time passed just now");
        assertThat(calculateRemainingMaxAge(OffsetDateTime.now().minusDays(100), 100)).isEqualTo(0).as("time passed long time ago");
        assertThat(calculateRemainingMaxAge(OffsetDateTime.now().minusYears(70), 100)).isEqualTo(0).as("time passed very long time ago");
    }

    @Test(expected = UnsupportedOperationException.class)
    public void throwsExceptionForOnSuccessfulLogin() {
        rmm.onSuccessfulLogin(subject, new GumtreeAuthenticationToken(), createAuthenticationInfo());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void throwsExceptionForOnFailedLogin() {
        rmm.onFailedLogin(subject, new GumtreeAuthenticationToken(), new AuthenticationException());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void throwsExceptionForGetRememberedPrincipals() {
        rmm.getRememberedPrincipals(subjectContext);
    }

    @Test(expected = UnsupportedOperationException.class)
    public void throwsExceptionForOnRememberedPrincipalFailure() {
        rmm.onRememberedPrincipalFailure(new RuntimeException(), subjectContext);
    }

    @Test(expected = UnsupportedOperationException.class)
    public void throwsExceptionForOnLogout() {
        rmm.onLogout(subject);
    }

    private UserAuthenticationInfo createAuthenticationInfo() {
        UserAuthenticationInfo authInfo = new UserAuthenticationInfo();
        GumtreeAccessToken gumtreeAccessToken = new GumtreeAccessToken();
        gumtreeAccessToken.setValue("token");
        authInfo.setUsername("<EMAIL>");
        authInfo.setAccessToken(gumtreeAccessToken);
        return authInfo;
    }

    private byte[] decrypt(byte[] value) {
        return cipherService().decrypt(java.util.Base64.getDecoder().decode(value), ENC_KEY).getBytes();
    }

    private static CipherService cipherService() {
        AesCipherService cipherService = new AesCipherService();
        cipherService.setKeySize(128);
        cipherService.setInitializationVectorSize(128);
        return cipherService;
    }

    private static SimpleCookie rememberMeCookieTemplate() {
        final SimpleCookie cookieTemplate = new SimpleCookie("rememberMe");
        cookieTemplate.setMaxAge(100);
        cookieTemplate.setHttpOnly(true);
        cookieTemplate.setSecure(true);
        cookieTemplate.setDomain("dev.gumtree.com");
        return cookieTemplate;
    }

    private ArgumentCaptor<String> initMocksForSerialization() {
        ArgumentCaptor<String> httpHeader = ArgumentCaptor.forClass(String.class);
        when(subject.getServletRequest()).thenReturn(request);
        when(subject.getServletResponse()).thenReturn(response);
        when(request.getContextPath()).thenReturn("/");
        return httpHeader;
    }
}
