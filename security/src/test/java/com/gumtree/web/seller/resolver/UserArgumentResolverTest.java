package com.gumtree.web.seller.resolver;

import com.gumtree.api.User;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.web.security.UserSession;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserArgumentResolverTest {
    @InjectMocks private UserArgumentResolver argumentResolver;
    @Mock private ApiCallExecutor apiCallExecutor;
    @Mock private UserSession userSession;
    @Mock private ApiCallResponse<User> apiCallResponse;

    @Test
    public void shouldResolveUserArgument() throws Exception {
        User mockUser = new User();

        // given
        when(userSession.getUsername()).thenReturn("<EMAIL>");
        when(apiCallExecutor.call(new GetUserApiCall("<EMAIL>"))).thenReturn(apiCallResponse);
        when(apiCallResponse.getResponseObject()).thenReturn(mockUser);

        // when
        Object user = argumentResolver.resolveArgument(null, null, null, null);

        assertThat(user.equals(mockUser), is(true));
    }
}
