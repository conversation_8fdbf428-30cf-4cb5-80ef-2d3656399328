package com.gumtree.web.security.shiro;

import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.login.LoginUtils;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 *
 */
public class StartPostAdFlowFilterTest extends BaseShiroTest {

    private LoginUtils loginUtils;

    private SecurityHelper securityHelper;

    private Subject subject;

    private StartPostAdFlowFilter filter;

    private ServletRequest request;

    private ServletResponse response;

    @Before
    public void init() {
        loginUtils = mock(LoginUtils.class);
        securityHelper = mock(SecurityHelper.class);
        subject = mock(Subject.class);
        request = mock(HttpServletRequest.class);
        response = mock(ServletResponse.class);

        filter = new StartPostAdFlowFilter(loginUtils, securityHelper);
        setSubject(subject);
    }

    @Test
    public void userCanPassThroughIfExistingRememberedUserWithValidAccessToken() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void userCanNotPassThroughIfExistingRememberedUserWithInvalidAccessToken() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(false);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(false));
    }

    @Test
    public void userCanPassThroughIfExistingAuthenticatedUser() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isRemembered()).thenReturn(false);
        when(subject.isAuthenticated()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void newUserCanPassThroughIfEmailIsInSessionAndPassThroughFlagAllowsAccess() {
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(false);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void newUserCannotPassThroughIfEmailIsInSessionButPassThroughFlagDoesNotAllowAcces() {
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(false));
    }

    @Test
    public void newUserCannotPassThroughIfPassThroughFlagAllowsAccessButEmailAddressNotInSession() {
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(false);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(false));
    }
}
