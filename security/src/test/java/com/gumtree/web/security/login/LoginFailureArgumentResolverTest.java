package com.gumtree.web.security.login;

import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Test;
import org.springframework.core.MethodParameter;
import org.springframework.web.context.request.NativeWebRequest;

import javax.servlet.http.HttpServletRequest;

import static com.gumtree.web.security.login.LoginFailure.UNKNOWN_ACCOUNT;
import static com.gumtree.web.security.shiro.GumtreeFormAuthenticationFilter.LOGIN_FAILURE;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class LoginFailureArgumentResolverTest {

    private NativeWebRequest nativeWebRequest;

    private HttpServletRequest httpServletRequest;

    private LoginFailureArgumentResolver resolver = new LoginFailureArgumentResolver();

    @Before
    public void init() {
        nativeWebRequest = mock(NativeWebRequest.class);
        httpServletRequest = mock(HttpServletRequest.class);
        when(nativeWebRequest.getNativeRequest()).thenReturn(httpServletRequest);
    }

    @Test
    public void supportsLoginFailureClass() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) LoginFailure.class);
        assertThat(resolver.supportsParameter(parameter), equalTo(true));
    }

    @Test
    public void doesNotSupportSomeOtherClass() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) String.class);
        assertThat(resolver.supportsParameter(parameter), equalTo(false));
    }

    @Test
    public void returnsLoginFailureWhenExistsAsAttributeInHttpServletRequest() throws Exception {
        when(httpServletRequest.getAttribute(LOGIN_FAILURE)).thenReturn(UNKNOWN_ACCOUNT);
        LoginFailure failure = (LoginFailure) resolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(failure, equalTo((LoginFailure)UNKNOWN_ACCOUNT));
    }

    @Test
    public void returnsNullWhenLoginFailureDoesNotExistInHttpServletRequest() throws Exception {
        Object o = resolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(o, CoreMatchers.nullValue());
    }
}
