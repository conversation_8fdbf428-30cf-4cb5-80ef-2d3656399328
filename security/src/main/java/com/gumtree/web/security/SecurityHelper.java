package com.gumtree.web.security;

import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.VerifyAccessTokenRequest;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;
import com.gumtree.user.service.support.factory.LogoutRequestFactory;
import com.gumtree.userapi.model.GumtreeAccessToken;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.shiro.subject.Subject;

import java.util.Collection;

import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.metrics.AuthenticationMetrics.incrementCounter;

public class SecurityHelper {

    private final UserServiceFacade userServiceFacade;
    private final MeterRegistry meterRegistry;

    public SecurityHelper(UserServiceFacade userServiceFacade, MeterRegistry meterRegistry) {
        this.userServiceFacade = userServiceFacade;
        this.meterRegistry = meterRegistry;
    }

    /**
     * Check the access token and the token is invalid then log user out
     * @param subject the subject to verify
     * @return true if the token is valid otherwise false
     */
    public boolean verifyAccessTokenAndLogoutIfInvalid(Subject subject) {
        Collection<GumtreeAccessToken> accessTokens = subject.getPrincipals().byType(GumtreeAccessToken.class);
        if (!accessTokens.isEmpty()) {
            VerifyAccessTokenRequest request = new VerifyAccessTokenRequest();
            request.setUsername(subject.getPrincipals().getPrimaryPrincipal().toString());
            request.setToken(GumtreeAccessTokenFactory.createFromString(accessTokens.iterator().next().getValue()));
            ApiResponse<Boolean> response = userServiceFacade.verifyAccessToken(request);
            boolean accessTokenIsValid = response.isDefined() && Boolean.TRUE.equals(response.get());

            if (!accessTokenIsValid) {
                incrementCounter(meterRegistry, Action.ACCESS_TOKEN, Status.FAILURE);
                removeOfflineAccessToken();
                subject.logout();
            } else {
                incrementCounter(meterRegistry, Action.ACCESS_TOKEN, Status.SUCCESS);
            }

            return accessTokenIsValid;
        }

        subject.logout();
        return false;
    }

    public void removeOfflineAccessToken() {
        GtSecurityUtils.getGumtreeAccessToken().ifPresent(gumtreeAccessToken ->
                userServiceFacade.logout(LogoutRequestFactory.createLogoutRequest(gumtreeAccessToken.getValue())));
    }
}
