package com.gumtree.web.security.shiro;

import com.gumtree.api.Account;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.support.factory.LogoutRequestFactory;
import com.gumtree.web.security.GtSecurityUtils;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.UserSessionBean;
import com.gumtree.web.security.UserSessionDataService;
import com.gumtree.web.security.exception.InvalidUserException;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import com.gumtree.web.security.login.LoginUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of {@link com.gumtree.web.security.UserSession} that provides implementation for methods by
 * delegating to the Shiro {@link org.apache.shiro.subject.Subject}. Is passed a {@code UserSessionBean} that
 * retrieves session info for the current user (bound to current user session)
 */
public final class ShiroSubjectUserSession implements UserSession {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShiroSubjectUserSession.class);

    private LoginUtils loginUtils;

    private UserSecurityManager userSecurityManager;

    private UserSessionDataService userSessionDataService;

    private UserServiceFacade userServiceFacade;

    public ShiroSubjectUserSession(UserSecurityManager userSecurityManager,
                                   LoginUtils loginUtils,
                                   UserSessionDataService userSessionDataService,
                                   UserServiceFacade userServiceFacade) {
        this.userSecurityManager = userSecurityManager;
        this.loginUtils = loginUtils;
        this.userSessionDataService = userSessionDataService;
        this.userServiceFacade = userServiceFacade;
    }

    @Override
    public synchronized boolean isSuperUser() {
        UserSessionBean userSessionBean = init();
        return userSessionBean.getUser() != null ? userSessionBean.getUser().isSuperUser() : false;
    }

    @Override
    public synchronized boolean isProUser() {
        if (isAuthenticated() && !UserLoginStatus.NEW_UNREGISTERED.equals(getUserType())) {
            UserSessionBean userSessionBean = init();
            return (userSessionBean.getUser().getType() == UserType.PRO);
        }
        return false;
    }

    @Override
    public void validate() {
        init();
    }

    @Override
    public synchronized String getUsername() {
        UserSessionBean userSessionBean = init();
        return userSessionBean.getUser().getEmail();
    }

    @Override
    public synchronized BushfireApiKey getApiKey() {
        UserSessionBean userSessionBean = init();
        return userSessionBean.getUser().getApiKey();
    }

    @Override
    public synchronized Long getSelectedAccountId() {
        UserSessionBean userSessionBean = init();
        if (userSessionBean.getSelectedAccountId() == null) {
            userSessionBean.setSelectedAccountId(getDefaultAccountId(userSessionBean));
            userSessionDataService.persistSessionBean(getLoginName(), userSessionBean);
        }
        return userSessionBean.getSuperUserOverrideAccountId() != null
                ? userSessionBean.getSuperUserOverrideAccountId()
                : userSessionBean.getSelectedAccountId();
    }

    @Override
    public synchronized void setSelectedAccountId(Long accountId) {
        UserSessionBean userSessionBean = init();
        userSessionBean.setSelectedAccountId(accountId);

        if (userSessionBean.getSuperUserOverrideAccountId()!=null) {
            userSessionBean.setSuperUserOverrideAccountId(null);
            List<Account> accounts = userSecurityManager.getUserAccounts(getLoginName(), this);

            // Set the user type based on the user's selectable accounts
            UserType userType = accounts.stream().anyMatch(account -> account.isPro()) ? UserType.PRO : UserType.STANDARD;
            userSessionBean.getUser().setType(userType);
        }

        userSessionDataService.persistSessionBean(getLoginName(), userSessionBean);
    }

    @Override
    public synchronized void setSuperUserOverrideAccountId(Long superUserOverrideAccountId) {
        UserSessionBean userSessionBean = init();
        userSessionBean.setSuperUserOverrideAccountId(superUserOverrideAccountId);

        // Set the user type based on if the override account is PRO / STANDARD
        Account overrideAccount = userSecurityManager.getAccount(superUserOverrideAccountId, this);
        userSessionBean.getUser().setType(overrideAccount.isPro()?UserType.PRO:UserType.STANDARD);

        userSessionDataService.persistSessionBean(getLoginName(), userSessionBean);
    }

    @Override
    public synchronized List<Account> getSelectableAccounts() {
        UserSessionBean userSessionBean = init();
        List<Account> accounts = userSecurityManager.getUserAccounts(getLoginName(), this);
        if (userSessionBean.getSuperUserOverrideAccountId() != null) {
            accounts.add(userSecurityManager.getAccount(userSessionBean.getSuperUserOverrideAccountId(), this));
        }

        return accounts;
    }

    @Override
    public synchronized User getUser() {
        UserSessionBean userSessionBean = init();
        return userSessionBean.getUser();
    }

    @Override
    public UserLoginStatus getUserType() {
        UserSessionBean userSessionBean = init();
        return currentUserType(userSessionBean);
    }

    @Override
    public void logout() {

        GtSecurityUtils.getGumtreeAccessToken().ifPresent(gumtreeAccessToken ->
                userServiceFacade.logout(LogoutRequestFactory.createLogoutRequest(gumtreeAccessToken.getValue())));

        UserSessionBean userSessionBean = init();
        if (currentUserType(userSessionBean).equals(UserLoginStatus.EXISTING)) {
            getSubject().logout();
        }

        loginUtils.clearNewUserEmailAddressFromSession();
        loginUtils.setNewUserMustLoginToStartPostAdFlow(true);
        userSessionDataService.removeUserSessionBean(userSessionBean.getUser().getEmail());
    }

    @Override
    public void clearSession() {
        if (getSubject().isRemembered()) {
            getSubject().logout();
        }
    }

    @Override
    public ApiCallResponse<User> registerNewUser(RegisterUserBean registerUserBean) {
        UserSessionBean userSessionBean = init();

        if (currentUserType(userSessionBean).equals(UserLoginStatus.NEW_UNREGISTERED)) {
            ApiCallResponse<User> response = userSecurityManager.registerNewUser(registerUserBean);
            if (!response.isErrorResponse()) {
                userSessionBean.setUser(response.getResponseObject());
                userSessionBean.setNewUser(true);

                userSessionDataService.persistSessionBean(registerUserBean.getEmailAddress(), userSessionBean);
            }
            return response;
        }
        throw new IllegalStateException("User is already registered");
    }

    @Override
    public boolean isAuthenticated() {
        return getSubject().isRemembered()
                || getSubject().isAuthenticated()
                || loginUtils.getNewUserEmailAddressFromSession() != null;
    }

    @Override
    public boolean isLoggedIn() {
        return getSubject().isRemembered()
                || getSubject().isAuthenticated();
    }

    @Override
    public void extendUserSession() {
        UserSessionBean userSessionBean = init();
        userSessionDataService.extendUserSessionBean(userSessionBean.getUser().getEmail());
    }

    private String getLoginName() {
        String loginname = (String) getSubject().getPrincipal();

        if (StringUtils.isEmpty(loginname)) {
            // Could be a new unregistered user
            loginname = loginUtils.getNewUserEmailAddressFromSession();
        }

        return loginname;
    }

    private UserSessionBean init() {
        mustBeRecognisedUser();

        String loginName = getLoginName();
        UserSessionBean userSessionBean = userSessionDataService.getUserSessionBean(loginName);

        if (loginUtils.getAndSetUserDirty(false)) {
            userSessionBean.setInitialized(false);
        }

        if (!userSessionBean.isInitialized()) {

            Subject subject = getSubject();

            if (subject.isAuthenticated() || subject.isRemembered()) {
                User user = loadUser();
                userSessionBean.setUser(user);
                userSessionBean.setNewUser(false);
            } else if (loginUtils.getNewUserEmailAddressFromSession() != null) {
                User user = loadNewUser();
                userSessionBean.setUser(user);
                userSessionBean.setNewUser(true);
            } else {
                throw createUserNotRecognisedException(getSubject());
            }

            userSessionBean.setSelectedAccountId(getDefaultAccountId(userSessionBean));
            userSessionBean.setInitialized(true);

            userSessionDataService.persistSessionBean(loginName, userSessionBean);
        }

        return userSessionBean;
    }

    private User loadNewUser() {
        Optional<User> apiUser = getApiUser(loginUtils.getNewUserEmailAddressFromSession());

        User user;
        if (apiUser.isPresent()) {
            user = apiUser.get();
        } else {
            user = new User();
            user.setEmail(loginUtils.getNewUserEmailAddressFromSession());
            user.setApiKey(userSecurityManager.getDefaultApiKey());
        }

        // From here on in, a new user trying to start a new post will see the login screen again
        loginUtils.setNewUserMustLoginToStartPostAdFlow(true);

        return user;
    }

    private Long getDefaultAccountId(UserSessionBean userSessionBean) {
        try {
            return Long.parseLong(getLoginName());
        } catch (Exception e) {
            return userSessionBean.getUser().getAccountIds().size() > 0
                    ? userSessionBean.getUser().getAccountIds().get(0)
                    : null;
        }
    }

    private User loadUser() {
        User user = getApiUser();
        if (user.getAccountIds() == null || user.getAccountIds().size() == 0) {
            throw new InvalidUserException("User " + user.getId() + " does not have any accounts");
        }

        if (user.getApiKey() == null) {
            throw new InvalidUserException("User " + user.getId() + " does not have an api key");
        }

        return user;
    }

    private UserLoginStatus currentUserType(UserSessionBean userSessionBean) {
        if (!userSessionBean.isNewUser()) {
            return UserLoginStatus.EXISTING;
        } else if (getSelectedAccountId() != null) {
            return UserLoginStatus.NEW_REGISTERED;
        }
        return UserLoginStatus.NEW_UNREGISTERED;
    }

    private User getApiUser() {
        try {
            return userSecurityManager.getExistingUser(getLoginName());
        } catch (RuntimeException ex) {
            throw createUserNotRecognisedException(getSubject());
        }
    }

    private Optional<User> getApiUser(String loginName) {
        try {
            return Optional.of(userSecurityManager.getExistingUser(loginName));
        } catch (RuntimeException ex) {
            return Optional.empty();
        }
    }

    private Subject getSubject() {
        return SecurityUtils.getSubject();
    }

    private void mustBeRecognisedUser() {

        if (isAuthenticated()) {
            return;
        }

        LOGGER.debug("Found invalid state in Shiro Subject - Subject: " + String.valueOf(getSubject().getPrincipal())
                + ", isRemembered: " + getSubject().isRemembered()
                + ", isAuthenticated: " + getSubject().isAuthenticated()
                + ", isNewUser:" + (loginUtils.getNewUserEmailAddressFromSession() != null)
                + ", sessionIsCreated: " + (getSubject().getSession(false) != null));

        throw createUserNotRecognisedException(getSubject());
    }

    private UserNotRecognisedException createUserNotRecognisedException(Subject subject) {
        return new UserNotRecognisedException("User " + String.valueOf(subject.getPrincipal()) + " not recognised");
    }
}
