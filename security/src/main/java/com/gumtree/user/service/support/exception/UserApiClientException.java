package com.gumtree.user.service.support.exception;

import com.gumtree.shared.client.FeignHystrixClientException;
import com.gumtree.user.service.model.UserApiErrors;

import java.util.Optional;

public class UserApiClientException extends FeignHystrixClientException {
    private UserApiErrors userApiErrors;

    public UserApiClientException(String message) {
        super(message);
    }

    public UserApiClientException(UserApiErrors userApiErrors) {
        super(userApiErrors.getErrorCode());
        this.userApiErrors =userApiErrors;
    }

    public Optional<UserApiErrors> getUserApiErrors() {
        return userApiErrors == null ? Optional.empty() : Optional.of(userApiErrors);
    }
}
