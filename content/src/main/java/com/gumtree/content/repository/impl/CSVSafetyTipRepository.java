package com.gumtree.content.repository.impl;

import com.google.common.collect.Multimap;
import com.gumtree.common.util.delimited.DelimitedMapLineMapper;
import com.gumtree.common.util.delimited.DelimitedMultimapParser;
import com.gumtree.content.domain.SafetyTip;
import com.gumtree.content.repository.SafetyTipRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * SafetyTipRepository implementation for use with CSV files
 */
@Component
public class CSVSafetyTipRepository implements SafetyTipRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CSVSafetyTipRepository.class);

    private Resource safetyTipData;

    private DelimitedMultimapParser delimitedMultimapParser;

    private DelimitedMapLineMapper<Long, SafetyTip> delimitedMapLineMapper;

    private Multimap<Long, SafetyTip> safetyTipsMap;

    @Autowired
    public CSVSafetyTipRepository(DelimitedMultimapParser delimitedMultimapParser,
                                  DelimitedMapLineMapper<Long, SafetyTip> delimitedMapLineMapper,
                                  @Qualifier("safetyTipData") Resource safetyTipData) {

        this.delimitedMultimapParser = delimitedMultimapParser;
        this.delimitedMapLineMapper = delimitedMapLineMapper;
        this.safetyTipData = safetyTipData;
        try {
            loadSafetyTipData();
        } catch (IOException e) {
            LOGGER.error("Unable to load SafetyTips data", e);
        }
    }

    @Override
    public List<SafetyTip> findByCategoryId(Long categoryId) {
        List<SafetyTip> tips = new ArrayList<SafetyTip>(safetyTipsMap.get(categoryId));
        Collections.sort(tips);
        return tips;
    }

    private void loadSafetyTipData() throws IOException {
        safetyTipsMap = delimitedMultimapParser.parse(
                new InputStreamReader(safetyTipData.getInputStream()),
                "\\|",
                delimitedMapLineMapper);
    }
}
