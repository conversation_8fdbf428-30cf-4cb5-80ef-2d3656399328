package com.gumtree.content.repository;

import com.gumtree.content.domain.SafetyTip;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;

/**
 */
public class SafetyTipLineMapperTest {

    @Test
    public void mapLineToSafetyTip() {
        String[] line = new String[] {"1", "2", "test message"};
        SafetyTip safetyTip = new SafetyTipLineMapper().map(line);
        assertThat(safetyTip.getCategoryId(), equalTo(1L));
        assertThat(safetyTip.getPriority(), equalTo(2));
        assertThat(safetyTip.getMessage(), equalTo("test message"));
    }

    @Test
    public void handleBadNumberFormat() {
        String[] line = new String[] {"1q", "2", "test message"};
        SafetyTip safetyTip = new SafetyTipLineMapper().map(line);
        assertThat(safetyTip, nullValue());
    }
}
