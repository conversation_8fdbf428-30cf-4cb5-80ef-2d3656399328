package com.gumtree.web.cookie;

import com.gumtree.common.properties.GtProps;

public enum GumtreeCookieProperty implements GtProps.GtProperty {

    COOKIES_DOMAIN("gumtree.cookies.default_domain", "Cookies domain"),
    COOKIES_SECURE("gumtree.cookies.secure", "Handle cookies as secure");

    private String propertyName;
    private String description;


    private GumtreeCookieProperty(String propertyName, String description) {
        this.propertyName = propertyName;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return propertyName;
    }

    public String getDescription() {
        return description;
    }
}
