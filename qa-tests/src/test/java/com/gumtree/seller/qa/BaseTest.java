package com.gumtree.seller.qa;

import com.gumtree.seller.qa.domain.TDSFixtures;
import com.gumtree.seller.qa.utils.SellerHttpClient;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.common.properties.GtProps;
import com.gumtree.testdata.service.AdvertsApi;
import com.gumtree.testdata.service.UsersApi;
import com.gumtree.testdata.service.model.*;
import feign.Logger;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;

import static java.lang.Boolean.TRUE;

public abstract class BaseTest {
    protected static final String TEST_TITLE = "Test Title";
    protected static final String TEST_DESCRIPTION = "Test ad description for sale on QA Tests platform. One, two, three, four, five, six, seven, eight, nine, ten.";

    private AdvertsApi tdsAdvertsApi;
    private UsersApi tdsUsersApi;

    protected SellerHttpClient sellerClient;

    @BeforeAll
    public static void beforeAll() throws ConfigurationException {
        GtPropertiesInitializer.init("qa-tests");
    }

    @BeforeEach
    public void before() {
        tdsAdvertsApi = buildTdsAdvertApi();
        tdsUsersApi = buildTdsUsersApi();
        sellerClient = new SellerHttpClient(GtProps.getStr(QaConfigProps.SELLER_URL));
    }

    /* -- Utility methods --*/

    protected User createUser(boolean isPro) {
        TestUser user = isPro ? TDSFixtures.user().proUser(TRUE) : TDSFixtures.user();
        return tdsUsersApi.createUser(user);
    }

    protected Ad createAdvert(TestAdvert tdsAdvert) {
        return tdsAdvertsApi.createAdvert(tdsAdvert);
    }

    protected Ad fetchAd(Long id) {
        return tdsAdvertsApi.getAdvert(id);
    }

    /* -- Setup Clients --*/

    private static UsersApi buildTdsUsersApi() {
        var builder = new com.gumtree.testdata.service.api.ApiClient().setBasePath(GtProps.getStr(QaConfigProps.TDS_URL));
        builder.getFeignBuilder().logLevel(Logger.Level.FULL);
        return builder.buildClient(UsersApi.class);
    }

    private static AdvertsApi buildTdsAdvertApi() {
        var builder = new com.gumtree.testdata.service.api.ApiClient().setBasePath(GtProps.getStr(QaConfigProps.TDS_URL));
        builder.getFeignBuilder().logLevel(Logger.Level.FULL);
        return builder.buildClient(AdvertsApi.class);
    }
}
